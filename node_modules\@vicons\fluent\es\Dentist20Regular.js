import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M12.935 5.598a.5.5 0 0 0 0 1c.291 0 .539.086.719.246c.176.157.333.423.374.869a.5.5 0 1 0 .995-.09c-.059-.654-.307-1.173-.706-1.527c-.395-.35-.892-.498-1.382-.498zM8.94 3.928c-.936-.756-1.975-.917-2.905-.735A4.259 4.259 0 0 0 3.777 4.46c-.603.656-1.194 1.486-1.241 2.7c-.022.602.067 1.475.532 2.129l.002.002c.613.851.922 1.475 1.048 2.287c.04.26.063.543.068.867c.005.644.14 1.529.466 2.334c.323.795.873 1.608 1.77 1.939l.022.008l.023.006c.524.14 1.012.09 1.38-.255c.324-.303.46-.754.535-1.16c.07-.376.105-.812.14-1.241l.012-.153c.04-.491.086-.98.181-1.442c.266-1.273.647-1.497.824-1.55c.491-.127.779-.041.97.098c.278.202.49.615.638 1.224c.143.593.2 1.264.25 1.877c.072.866.255 1.654.604 2.158c.18.262.437.49.784.565c.35.075.69-.03.99-.224c1.104-.704 1.743-2.167 1.918-4.105c.08-.871.169-1.294.298-1.628c.1-.26.223-.47.418-.802a37.592 37.592 0 0 0 .363-.632c.244-.436.645-1.23.69-2.16c.048-.966-.29-2.033-1.431-2.961c-1.09-.886-2.106-1.207-3.013-1.158c-.898.05-1.617.458-2.128.896l-.013.01l-.011.013c-.5.49-.73.577-.88.57c-.168-.006-.442-.137-1.005-.705l-.02-.02l-.021-.017zM4.513 5.137a3.258 3.258 0 0 1 1.713-.961c.663-.13 1.387-.024 2.065.515c.54.542 1.06.956 1.653.981c.616.026 1.123-.37 1.609-.844c.397-.337.91-.613 1.52-.646c.608-.033 1.387.17 2.327.935c.888.722 1.096 1.48 1.064 2.136c-.034.691-.339 1.317-.565 1.722a20.017 20.017 0 0 1-.32.558c-.212.359-.39.661-.521 1.002c-.18.467-.279 1.003-.36 1.899c-.165 1.82-.746 2.897-1.461 3.353l-.003.002c-.155.1-.226.09-.24.086c-.018-.004-.08-.025-.17-.156c-.195-.28-.362-.848-.43-1.672l-.001-.012c-.05-.596-.111-1.34-.274-2.016c-.16-.665-.444-1.379-1.021-1.799c-.486-.353-1.1-.443-1.82-.255l-.011.003c-.725.21-1.24.915-1.531 2.31c-.11.528-.159 1.073-.199 1.563l-.012.15c-.035.44-.066.822-.126 1.142c-.07.37-.16.543-.235.614c-.031.03-.115.1-.417.024c-.502-.194-.894-.693-1.168-1.368a5.7 5.7 0 0 1-.393-1.968v-.004a7.255 7.255 0 0 0-.08-1.006c-.159-1.025-.56-1.797-1.224-2.717c-.274-.387-.365-.992-.347-1.51c.034-.867.442-1.477.978-2.06z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Dentist20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
