import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M17 12v-1.5a.569.569 0 0 0-.07-.273a.368.368 0 0 0-.172-.149a.868.868 0 0 0-.235-.062a2.08 2.08 0 0 0-.257-.016H16V5.117c.031-.068.076-.148.133-.242c.057-.094.112-.198.164-.312c.052-.115.099-.222.14-.32A.66.66 0 0 0 16.5 4a2.053 2.053 0 0 0-.07-.32a7.432 7.432 0 0 0-.157-.493a45.772 45.772 0 0 0-.18-.5a5.543 5.543 0 0 1-.116-.343a.57.57 0 0 0-.188-.25A.469.469 0 0 0 15.5 2h-2a.55.55 0 0 0-.297.102a.46.46 0 0 0-.18.242c-.026.078-.067.195-.125.351a13.75 13.75 0 0 0-.171.5c-.058.177-.11.341-.157.493c-.047.15-.07.255-.07.312c0 .073.02.156.063.25c.041.094.09.198.148.313a17.292 17.292 0 0 0 .289.554V10h-.266c-.093 0-.182.005-.265.016a.72.72 0 0 0-.235.07a.436.436 0 0 0-.171.148a.477.477 0 0 0-.063.266V12h5zm0 1h-5v2.555c0 .338.068.656.203.953c.136.297.318.557.547.781A2.491 2.491 0 0 0 14.5 18c.333 0 .651-.065.953-.195a2.68 2.68 0 0 0 .797-.524c.23-.218.412-.476.547-.773a2.27 2.27 0 0 0 .203-.953V13zM5.78 2.126a.5.5 0 0 1 .22.415v3.457a1 1 0 0 0 2 0V2.541a.5.5 0 0 1 .688-.463A4.501 4.501 0 0 1 9 10.282v5.716a2 2 0 1 1-4 0v-5.716a4.5 4.5 0 0 1 .312-8.204a.5.5 0 0 1 .467.048z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WrenchScrewdriver20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
