<template>
  <n-scrollbar style="max-height: 92vh">
    <div class="split-detail-container">
      <!-- 页面头部 -->
      <div class="header">
        <n-page-header @back="handleBack">
          <template #title>
            <span>订单拆分详情</span>
          </template>
          <template #subtitle>
            <span style="font-weight: bold;">订单号: {{ orderInfo.orderNo }}</span>
          </template>
        </n-page-header>
      </div>

      <!-- 订单基本信息 -->
      <div class="order-info">
        <n-card title="订单基本信息" :bordered="false">
          <n-descriptions :column="3" bordered >
            <n-descriptions-item label="订单号" >
              {{ orderInfo.orderNo }}
            </n-descriptions-item>
            <n-descriptions-item label="公司名称">
              {{ orderInfo.customerName }}
            </n-descriptions-item>
            <n-descriptions-item label="项目名称">
              
              <n-input v-model:value="orderInfo.PrivateDescSeg7" type="text" placeholder="未填写项目地址" />
            </n-descriptions-item>
          </n-descriptions>
        </n-card>
      </div>

      <!-- 产品明细表格 -->
      <div class="product-detail">
        <n-card title="产品明细" :bordered="false">
          <n-data-table :columns="columns" :data="productList" :pagination="pagination" :bordered="false"
            :loading="loading" :row-key="(row: ProductItem) => row.uniqueId || row.ItemInfo.ItemCode"
            :single-line="false" :checked-row-keys="selectedItems" @update:checked-row-keys="handleSelectionChange" />
        </n-card>
      </div>

      <!-- 拆分控制区域 -->
      <div class="split-controls">
        <n-card title="拆分操作" :bordered="false">
          <n-space>
            <n-button type="primary" :disabled="splitMode" @click="enterSplitMode">
              开始拆分
            </n-button>
            <n-button v-if="splitMode" type="success" :loading="exportLoading" :disabled="!hasValidSplitData"
              @click="handleSplitExport">
              <template #icon>
                <n-icon>
                  <DocumentArrowDown20Regular />
                </n-icon>
              </template>
              导出拆分数据
            </n-button>
            <n-button v-if="splitMode" @click="cancelSplitMode">
              取消拆分
            </n-button>
            <n-button v-if="splitMode" type="warning" @click="confirmSplit">
              确认拆分
            </n-button>
          </n-space>
          <div v-if="splitMode" class="split-info">
            <p>已选择 {{ selectedItems.length }} 个产品进行拆分</p>
            <p>拆分说明：选择产品并设置拆分数量，确认后将从原订单中减少相应数量</p>
          </div>
        </n-card>
      </div>

      <!-- 操作按钮 -->
      <div class="actions">
        <n-space>
          <n-button type="primary" :disabled="splitMode" @click="handleExport">
            <template #icon>
              <n-icon>
                <DocumentArrowDown20Regular />
              </n-icon>
            </template>
            导出完整清单
          </n-button>
          <n-button @click="handleBack">
            返回
          </n-button>
        </n-space>
      </div>
    </div>
  </n-scrollbar>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, reactive, h, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import * as ExcelJS from 'exceljs'
import {
  NPageHeader,
  NCard,
  NDescriptions,
  NDescriptionsItem,
  NDataTable,
  NButton,
  NSpace,
  NIcon,
  NInputNumber,
  useMessage,
  useDialog,
  type DataTableColumns
} from 'naive-ui'
import { DocumentArrowDown20Regular, Print20Regular } from '@vicons/fluent'

// 定义产品项接口
interface ProductItem {
  OrderByQtyTU: string
  ItemInfo: {
    ItemCode: string
    ItemName: string
  }
  DescFlexField: {
    PrivateDescSeg2: string
  }
  // 新增字段用于拆分功能
  uniqueId?: string        // 唯一标识符
  checked?: boolean
  splitQty?: number
  remainingQty?: number
  originalQty?: number
}

// 定义订单信息接口
interface OrderInfo {
  orderNo: string
  customerName: string
  releaseTime: string
  PrivateDescSeg7: string
}

// Props定义
interface Props {
  orderData?: {
    PrivateDescSeg7: string
    orderNo: string
    customerName: string
    releaseTime: string
    SOLines: ProductItem[]
  }
}

const props = withDefaults(defineProps<Props>(), {
  orderData: () => ({
    orderNo: '',
    customerName: '',
    releaseTime: '',
    SOLines: [],
    PrivateDescSeg7: ''
  })
})

const router = useRouter()
const route = useRoute()
const message = useMessage()
const dialog = useDialog()

// 响应式数据
const loading = ref(false)
const productList = ref<ProductItem[]>([])
const orderInfo = ref<OrderInfo>({
  orderNo: '',
  customerName: '',
  releaseTime: '',
  PrivateDescSeg7: ''
})

// 拆分相关数据
const selectedItems = ref<string[]>([]) // 选中的产品ItemCode
const splitMode = ref(false) // 是否处于拆分模式
const exportLoading = ref(false) // 导出加载状态



// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [5, 10, 20, 50, 100],
  showQuickJumper: true,
  simple: false,
  disabled: false,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`,
  onChange: (page: number) => {
    pagination.page = page
    console.log('分页变化到第', page, '页')
  },
  onUpdatePageSize: (size: number) => {
    pagination.pageSize = size
    pagination.page = 1 // 重置到第一页
    console.log('页面大小变化为', size, '条/页')
  }
})

// 计算属性
const hasValidSplitData = computed(() => {
  return productList.value.some(item =>
    item.checked && (item.splitQty || 0) > 0
  )
})

// 处理行选择变化
const handleSelectionChange = (keys: string[]) => {
  selectedItems.value = keys
  console.log('选择变化:', keys)
  // 更新产品的选中状态
  productList.value.forEach(item => {
    item.checked = keys.includes(item.uniqueId || item.ItemInfo.ItemCode)
    if (!item.checked) {
      item.splitQty = 0
    }
  })
}

// 进入拆分模式
const enterSplitMode = () => {
  splitMode.value = true
  // 初始化拆分数据
  productList.value.forEach((item, index) => {
    // 确保每个产品都有唯一ID
    if (!item.uniqueId) {
      item.uniqueId = `${item.ItemInfo.ItemCode}_${index}_${Date.now()}`
    }
    item.originalQty = parseFloat(item.OrderByQtyTU)
    item.splitQty = 0
    item.remainingQty = item.originalQty
    item.checked = false
  })
  selectedItems.value = []
  message.info('已进入拆分模式，请选择要拆分的产品并设置数量')
}

// 取消拆分模式
const cancelSplitMode = () => {
  splitMode.value = false
  selectedItems.value = []
  // 重置拆分数据
  productList.value.forEach(item => {
    item.splitQty = 0
    item.checked = false
    item.remainingQty = parseFloat(item.OrderByQtyTU)
  })
  message.info('已取消拆分模式')
}

// 确认拆分
const confirmSplit = () => {
  const splitItems = productList.value.filter(item =>
    item.checked && (item.splitQty || 0) > 0
  )

  if (splitItems.length === 0) {
    message.warning('请选择要拆分的产品并设置拆分数量')
    return
  }

  // 预计算哪些项目会被删除
  const itemsToRemove = splitItems.filter(item => {
    const newQty = parseFloat(item.OrderByQtyTU) - (item.splitQty || 0)
    return newQty <= 0
  })

  // 如果有项目会被删除，显示确认对话框
  if (itemsToRemove.length > 0) {
    const itemNames = itemsToRemove.map(item => item.ItemInfo.ItemName).join('、')

    dialog.warning({
      title: '确认拆分操作',
      content: `拆分后以下 ${itemsToRemove.length} 个产品的剩余数量为0，将被删除：\n\n${itemNames}\n\n是否继续执行拆分操作？`,
      positiveText: '确认拆分',
      negativeText: '取消',
      onPositiveClick: () => {
        executeSplit(splitItems)
      }
    })
  } else {
    // 没有项目会被删除，直接执行拆分
    executeSplit(splitItems)
  }
}

// 执行拆分操作
const executeSplit = (splitItems: ProductItem[]) => {
  let removedCount = 0
  const itemsToRemoveIds: string[] = []

  // 更新原始数量
  splitItems.forEach(item => {
    const newQty = parseFloat(item.OrderByQtyTU) - (item.splitQty || 0)
    item.OrderByQtyTU = newQty.toString()
    item.remainingQty = newQty

    // 收集需要删除的项目ID
    if (newQty <= 0) {
      itemsToRemoveIds.push(item.uniqueId || item.ItemInfo.ItemCode)
      removedCount++
    }
  })

  // 从产品列表中删除剩余数量为0的项目
  productList.value = productList.value.filter(item =>
    !itemsToRemoveIds.includes(item.uniqueId || item.ItemInfo.ItemCode)
  )

  // 退出拆分模式
  splitMode.value = false
  selectedItems.value = []

  const message_text = removedCount > 0
    ? `成功拆分 ${splitItems.length} 个产品，删除了 ${removedCount} 个数量为0的项目`
    : `成功拆分 ${splitItems.length} 个产品`

  message.success(message_text)

  console.log('拆分完成:', {
    splitItemsCount: splitItems.length,
    removedItemsCount: removedCount,
    remainingItemsCount: productList.value.length
  })
}

// 导出拆分数据
const handleSplitExport = async () => {
  exportLoading.value = true

  try {
    const splitItems = productList.value.filter(item =>
      item.checked && (item.splitQty || 0) > 0
    )

    if (splitItems.length === 0) {
      message.warning('没有可导出的拆分数据')
      return
    }

    await createExcelFile(splitItems, true)
    message.success(`成功导出 ${splitItems.length} 个拆分产品的数据`)

  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 创建Excel文件
const createExcelFile = async (items: ProductItem[], isSplitData = false) => {
  const workbook = new ExcelJS.Workbook()


  // Sheet1: 项目名称
  const sheet1 = workbook.addWorksheet('项目名称')

  // 设置Sheet1样式
  sheet1.getColumn(1).width = 30
  sheet1.getColumn(1).font = { size: 12, bold: true }
  sheet1.getColumn(1).alignment = { vertical: 'middle', horizontal: 'center' }
  sheet1.getCell(1, 1).value = orderInfo.value.PrivateDescSeg7 || '未填写项目地址'
  sheet1.pageSetup.printArea='A1:A1'

  // Sheet2: 基本信息
  const sheet2 = workbook.addWorksheet('基本信息')
  sheet2.getCell(1, 1).value = orderInfo.value.customerName || '未知公司'
  sheet2.getCell(2, 1).value = '订单：' + orderInfo.value.orderNo || '订单：未知订单'
  sheet2.getCell(3, 1).value = orderInfo.value.releaseTime || '未知时间'

  // 设置Sheet2样式
  sheet2.getColumn(1).width = 40
  sheet2.getColumn(1).font = { size: 12, bold: true }
  sheet2.getColumn(1).alignment = { vertical: 'middle', horizontal: 'left' }
  sheet2.pageSetup.printArea='A1:A3'

  // Sheet3: 产品明细
  const sheet3 = workbook.addWorksheet('产品明细')

  // 设置表头
  sheet3.getCell(1, 1).value = '料号'
  sheet3.getCell(1, 2).value = '产品名称'
  sheet3.getCell(1, 3).value = '数量'

  // 设置表头样式
  const headerRow = sheet3.getRow(1)
  headerRow.font = { bold: true, size: 12 }
  headerRow.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFE0E0E0' }
  }
  headerRow.alignment = { vertical: 'middle', horizontal: 'center' }

  // 添加数据
  items.forEach((item, index) => {
    const row = index + 2
    sheet3.getCell(row, 1).value = item.ItemInfo.ItemCode
    sheet3.getCell(row, 2).value = item.ItemInfo.ItemName

    // 根据是否是拆分数据决定数量显示
    const quantity = isSplitData ? (item.splitQty || 0) : parseFloat(item.OrderByQtyTU)
    sheet3.getCell(row, 3).value = quantity
  })

  // 设置Sheet3列宽和样式
  sheet3.getColumn(1).width = 20 // 料号
  sheet3.getColumn(2).width = 30 // 产品名称
  sheet3.getColumn(3).width = 15 // 数量
  

  // 设置数据行样式和边框
  for (let i = 1; i <= items.length + 1; i++) {
    const row = sheet3.getRow(i)
    row.alignment = { vertical: 'middle', horizontal: 'center' }
    if (i > 1) {
      row.font = { size: 11 }
    }

    // 为每个单元格添加边框
    for (let j = 1; j <= 3; j++) {
      const cell = sheet3.getCell(i, j)
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }
    }
  }

  // 设置打印区域 - 忽略第一行，从第二行开始
  if (items.length > 0) {
    const totalRows = items.length + 1 // 包含表头的总行数
    const totalCols = 3 // 当前有3列：料号、产品名称、数量

    // 计算打印区域
    const printAreas = []
    const startRow = 2 // 从第二行开始（忽略表头）
    const endRow = totalRows // 到最后一行

    // 每6列为一个打印区域，当前只有3列，所以只有一个打印区域
    for (let colStart = 1; colStart <= totalCols; colStart += 6) {
      const colEnd = Math.min(colStart + 5, totalCols) // 每组最多6列，但不超过实际列数

      // 将列号转换为Excel列字母
      const startColLetter = String.fromCharCode(64 + colStart) // A, B, C...
      const endColLetter = String.fromCharCode(64 + colEnd)

      // 构建打印区域字符串，格式：A2:C10
      const printArea = `${startColLetter}${startRow}:${endColLetter}${endRow}`
      printAreas.push(printArea)
    }

    // 设置打印区域，多个区域用 && 连接
    if (printAreas.length > 0) {
      sheet3.pageSetup.printArea = printAreas.join('&&')
    }
  }

  // 生成文件
  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  })

  // 下载文件
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)

  const fileName = isSplitData
    ? `拆分明细_${orderInfo.value.orderNo}_${new Date().toISOString().slice(0, 10)}.xlsx`
    : `订单明细_${orderInfo.value.orderNo}_${new Date().toISOString().slice(0, 10)}.xlsx`

  link.setAttribute('download', fileName)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

// 表格列定义
const columns: DataTableColumns<ProductItem> = [
  {
    type: 'selection',
    disabled: (row: ProductItem) => !splitMode.value || parseFloat(row.OrderByQtyTU) <= 0
  },
  {
    title: '序号',
    key: 'index',
    width: 80,
    render: (_: ProductItem, index: number) => index + 1
  },
  {
    title: '料号',
    key: 'ItemCode',
    width: 150,
    render: (row: ProductItem) => row.ItemInfo.ItemCode
  },
  {
    title: '产品名称',
    key: 'ItemName',
    width: 200,
    render: (row: ProductItem) => row.ItemInfo.ItemName
  },
  {
    title: '原始数量',
    key: 'originalQty',
    className:'originalQty',
    align:'center',
    width: 100,
    
    render: (row: ProductItem) => parseFloat(row.OrderByQtyTU).toFixed(0)
  },
  {
    title: '拆分数量',
    key: 'splitQty',
    align:'center',
    width: 150,
    render: (row: ProductItem) => {
      if (!splitMode.value) {
        return '-'
      }
      return h('div', { class: 'split-qty-container' }, [
        h(NInputNumber, {
          value: row.splitQty || 0,
          min: 0,
          max: parseFloat(row.OrderByQtyTU),
          precision: 0,
          size: 'small',
          style: { width: '100px'},
          disabled: !row.checked,
          onUpdateValue: (value: number | null) => {
            row.splitQty = value || 0
            row.remainingQty = parseFloat(row.OrderByQtyTU) - (row.splitQty || 0)
          }
        })
      ])
    }
  },
  {
    title: '剩余数量',
    key: 'remainingQty',
    align:'center',
    className:'remainingQty',
    width: 100,
    
    render: (row: ProductItem) => {
      if (!splitMode.value) {
        return parseFloat(row.OrderByQtyTU).toFixed(0)
      }
      const remaining = parseFloat(row.OrderByQtyTU) - (row.splitQty || 0)
      return remaining.toFixed(0)
    }
  },
  {
    title: '备注',
    key: 'remark',
    render: (row: ProductItem) => {
      const desc = row.DescFlexField.PrivateDescSeg2 || ''
      const remarkMatch = desc.match(/备注:(.*)/)
      return remarkMatch ? remarkMatch[1].trim() : '无'
    }
  }
]

// 初始化数据
const initData = () => {
  loading.value = true

  try {
    console.log('当前路由参数:', route.params)
    console.log('当前路由查询参数:', route.query)
    console.log('Props数据:', props.orderData)

    // 从路由查询参数获取数据
    const routeData = route.query.data as string
    const storageKey = route.query.storageKey as string
    let data = props.orderData

    if (routeData && routeData !== 'undefined') {
      console.log('从路由查询参数获取数据:', routeData)
      data = JSON.parse(decodeURIComponent(routeData))
      console.log('解析后的数据:', data)
    } else if (storageKey) {
      console.log('从sessionStorage获取数据:', storageKey)
      const storageData = sessionStorage.getItem(storageKey)
      if (storageData) {
        data = JSON.parse(storageData)
        console.log('从sessionStorage解析的数据:', data)
        // 清理sessionStorage
        sessionStorage.removeItem(storageKey)
      }
    } else {
      console.log('使用Props数据')
    }


    if (!data.SOLines || data.SOLines.length === 0) {
      console.warn('没有产品数据')
      message.warning('该订单没有产品明细数据')
    }

    orderInfo.value = {
      orderNo: data.orderNo || '未知订单',
      customerName: data.customerName || '未知客户',
      releaseTime: data.releaseTime || '未知时间',
      PrivateDescSeg7: data.PrivateDescSeg7 || ''
    }

    // 初始化产品列表并添加拆分相关字段
    productList.value = (data.SOLines || []).map((item, index) => ({
      ...item,
      uniqueId: `${item.ItemInfo.ItemCode}_${index}_${Date.now()}`, // 生成唯一ID
      checked: false,
      splitQty: 0,
      remainingQty: parseFloat(item.OrderByQtyTU),
      originalQty: parseFloat(item.OrderByQtyTU)
    }))

    console.log('初始化完成:', {
      orderInfo: orderInfo.value,
      productList: productList.value,
      productCount: productList.value.length,
      uniqueIds: productList.value.map((item, index) => ({
        index: index + 1,
        itemCode: item.ItemInfo.ItemCode,
        uniqueId: item.uniqueId
      }))
    })

    // 验证唯一ID是否真的唯一
    const uniqueIds = productList.value.map(item => item.uniqueId)
    const duplicateIds = uniqueIds.filter((id, index) => uniqueIds.indexOf(id) !== index)
    if (duplicateIds.length > 0) {
      console.error('发现重复的唯一ID:', duplicateIds)
    } else {
      console.log('所有产品都有唯一的ID ✓')
    }


  } catch (error) {
    console.error('数据初始化失败:', error)
    message.error(`数据加载失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

// 返回上一页
const handleBack = () => {
  router.back()
}

// 导出Excel
const handleExport = async () => {
  try {
    await createExcelFile(productList.value, false)
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  }
}


// 监听路由查询参数变化
watch(() => route.query, (newQuery) => {
  console.log('路由查询参数变化:', newQuery)
  if (newQuery.data) {
    initData()
  }
}, { immediate: true })

// 组件挂载时初始化数据
onMounted(() => {
  console.log('组件挂载，开始初始化数据')
  initData()
})
</script>

<style scoped lang="less">
:deep(.remainingQty) {
  color: #3CB371 !important;
  font-weight: bold;
}
:deep(.originalQty) {
  color: #CD5C5C !important;
  font-weight: bold;
}
.split-detail-container {
  padding: 20px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  margin-bottom: 20px;
  background: white;
  border-radius: 8px;
  padding: 16px;
}

.order-info {
  margin-bottom: 20px;
}

.product-detail {
  margin-bottom: 20px;
}

.split-controls {
  margin-bottom: 20px;
}

.split-info {
  margin-top: 16px;
  padding: 12px;
  background-color: #f0f2f5;
  border-radius: 6px;
  font-size: 14px;
  color: #666;
}

.split-info p {
  margin: 4px 0;
}

.split-qty-container {
  display: flex;
  align-items: center;
}

.actions {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

:deep(.n-card) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.n-data-table) {
  .n-data-table-th {
    background-color: #fafafa;
    font-weight: 600;
  }
}

@media print {

  .header,
  .actions {
    display: none;
  }

  .split-detail-container {
    padding: 0;
    background: white;
  }
}
</style>
