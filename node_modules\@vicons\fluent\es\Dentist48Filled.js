import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M21.27 8.228c-2.328-2.164-5.132-2.479-7.581-1.943c-2.407.526-4.57 1.885-5.868 3.26c-1.6 1.693-3.103 3.863-3.23 6.998v.008c-.054 1.543.147 3.822 1.376 5.515l.138.192c1.235 1.71 1.965 2.722 2.327 3.736l.003.008c.414 1.134.425 2.263.44 3.865l.004.43c.022 2.22.655 5.784 2.354 8.386c.82 1.258 1.945 2.373 3.45 2.932l.058.022l.06.015c1.197.316 2.33.25 3.247-.428c.856-.633 1.272-1.622 1.513-2.542c.245-.941.363-2.043.456-3.134c.027-.32.052-.638.077-.957c.064-.81.127-1.622.226-2.442a20.94 20.94 0 0 1 .283-1.78c.35-1.673.752-2.642 1.139-3.217c.342-.51.687-.733 1.07-.871c1.135-.287 1.854-.139 2.342.138c.524.297.979.852 1.357 1.715c.777 1.776.998 4.285 1.183 6.551v.003c.19 2.254.664 4.311 1.552 5.636c.457.682 1.104 1.289 1.987 1.502c.902.218 1.787-.037 2.571-.54l.001-.001c2.87-1.845 4.527-5.787 4.98-10.81c.19-2.128.439-3.255.83-4.244c.317-.8.727-1.522 1.353-2.622l.002-.002c.191-.337.404-.71.64-1.131c.644-1.144 1.681-3.208 1.797-5.617c.12-2.498-.759-5.252-3.723-7.629c-2.83-2.293-5.46-3.127-7.807-3.002c-2.328.124-4.192 1.181-5.523 2.328l-.031.027l-.029.028c-1.307 1.282-1.921 1.525-2.333 1.507c-.46-.02-1.187-.378-2.655-1.855l-.017-.018l-.019-.017zm1.542 18.053l-.047.012l.088-.026a2.48 2.48 0 0 0-.041.014zm8.847-14.037c2.689 0 5.122 2.346 5.385 5.226a1.25 1.25 0 1 1-2.49.227c-.157-1.72-1.623-2.953-2.895-2.953a1.25 1.25 0 0 1 0-2.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Dentist48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
