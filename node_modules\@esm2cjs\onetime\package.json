{"name": "@esm2cjs/onetime", "version": "6.0.1-cjs.0", "description": "Ensure a function is only called once. This is a fork of sindresorhus/onetime, but with CommonJS support.", "license": "MIT", "repository": "esm2cjs/onetime", "funding": "https://github.com/sponsors/AlCalzone", "author": {"name": "<PERSON>", "email": "d.gries<PERSON>@gmx.net"}, "exports": {".": {"import": "./esm/index.js", "require": "./cjs/index.js"}, "./package.json": "./package.json"}, "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["cjs/", "esm/"], "keywords": ["once", "function", "one", "onetime", "func", "fn", "single", "call", "called", "prevent"], "dependencies": {"@esm2cjs/mimic-fn": "^4.0.0"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "publishConfig": {"access": "public"}, "main": "cjs/index.js", "module": "esm/index.js", "types": "esm/index.d.ts", "typesVersions": {"*": {"esm/index.d.ts": ["esm/index.d.ts"], "cjs/index.d.ts": ["esm/index.d.ts"], "*": ["esm/*"]}}, "xo": {"ignores": ["cjs", "**/*.test-d.ts"]}}