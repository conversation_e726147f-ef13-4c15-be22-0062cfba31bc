{"version": 3, "sources": ["../../esm/lib/kill.js"], "sourcesContent": ["import os from 'node:os';\nimport onExit from 'signal-exit';\n\nconst DEFAULT_FORCE_KILL_TIMEOUT = 1000 * 5;\n\n// Monkey-patches `childProcess.kill()` to add `forceKillAfterTimeout` behavior\nexport const spawnedKill = (kill, signal = 'SIGTERM', options = {}) => {\n\tconst killResult = kill(signal);\n\tsetKillTimeout(kill, signal, options, killResult);\n\treturn killResult;\n};\n\nconst setKillTimeout = (kill, signal, options, killResult) => {\n\tif (!shouldForceKill(signal, options, killResult)) {\n\t\treturn;\n\t}\n\n\tconst timeout = getForceKillAfterTimeout(options);\n\tconst t = setTimeout(() => {\n\t\tkill('SIGKILL');\n\t}, timeout);\n\n\t// Guarded because there's no `.unref()` when `execa` is used in the renderer\n\t// process in Electron. This cannot be tested since we don't run tests in\n\t// Electron.\n\t// istanbul ignore else\n\tif (t.unref) {\n\t\tt.unref();\n\t}\n};\n\nconst shouldForceKill = (signal, {forceKillAfterTimeout}, killResult) => isSigterm(signal) && forceKillAfterTimeout !== false && killResult;\n\nconst isSigterm = signal => signal === os.constants.signals.SIGTERM\n\t\t|| (typeof signal === 'string' && signal.toUpperCase() === 'SIGTERM');\n\nconst getForceKillAfterTimeout = ({forceKillAfterTimeout = true}) => {\n\tif (forceKillAfterTimeout === true) {\n\t\treturn DEFAULT_FORCE_KILL_TIMEOUT;\n\t}\n\n\tif (!Number.isFinite(forceKillAfterTimeout) || forceKillAfterTimeout < 0) {\n\t\tthrow new TypeError(`Expected the \\`forceKillAfterTimeout\\` option to be a non-negative integer, got \\`${forceKillAfterTimeout}\\` (${typeof forceKillAfterTimeout})`);\n\t}\n\n\treturn forceKillAfterTimeout;\n};\n\n// `childProcess.cancel()`\nexport const spawnedCancel = (spawned, context) => {\n\tconst killResult = spawned.kill();\n\n\tif (killResult) {\n\t\tcontext.isCanceled = true;\n\t}\n};\n\nconst timeoutKill = (spawned, signal, reject) => {\n\tspawned.kill(signal);\n\treject(Object.assign(new Error('Timed out'), {timedOut: true, signal}));\n};\n\n// `timeout` option handling\nexport const setupTimeout = (spawned, {timeout, killSignal = 'SIGTERM'}, spawnedPromise) => {\n\tif (timeout === 0 || timeout === undefined) {\n\t\treturn spawnedPromise;\n\t}\n\n\tlet timeoutId;\n\tconst timeoutPromise = new Promise((resolve, reject) => {\n\t\ttimeoutId = setTimeout(() => {\n\t\t\ttimeoutKill(spawned, killSignal, reject);\n\t\t}, timeout);\n\t});\n\n\tconst safeSpawnedPromise = spawnedPromise.finally(() => {\n\t\tclearTimeout(timeoutId);\n\t});\n\n\treturn Promise.race([timeoutPromise, safeSpawnedPromise]);\n};\n\nexport const validateTimeout = ({timeout}) => {\n\tif (timeout !== undefined && (!Number.isFinite(timeout) || timeout < 0)) {\n\t\tthrow new TypeError(`Expected the \\`timeout\\` option to be a non-negative integer, got \\`${timeout}\\` (${typeof timeout})`);\n\t}\n};\n\n// `cleanup` option handling\nexport const setExitHandler = async (spawned, {cleanup, detached}, timedPromise) => {\n\tif (!cleanup || detached) {\n\t\treturn timedPromise;\n\t}\n\n\tconst removeExitHandler = onExit(() => {\n\t\tspawned.kill();\n\t});\n\n\treturn timedPromise.finally(() => {\n\t\tremoveExitHandler();\n\t});\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAAe;AACf,yBAAmB;AAEnB,MAAM,6BAA6B,MAAO;AAGnC,MAAM,cAAc,CAAC,MAAM,SAAS,WAAW,UAAU,CAAC,MAAM;AACtE,QAAM,aAAa,KAAK,MAAM;AAC9B,iBAAe,MAAM,QAAQ,SAAS,UAAU;AAChD,SAAO;AACR;AAEA,MAAM,iBAAiB,CAAC,MAAM,QAAQ,SAAS,eAAe;AAC7D,MAAI,CAAC,gBAAgB,QAAQ,SAAS,UAAU,GAAG;AAClD;AAAA,EACD;AAEA,QAAM,UAAU,yBAAyB,OAAO;AAChD,QAAM,IAAI,WAAW,MAAM;AAC1B,SAAK,SAAS;AAAA,EACf,GAAG,OAAO;AAMV,MAAI,EAAE,OAAO;AACZ,MAAE,MAAM;AAAA,EACT;AACD;AAEA,MAAM,kBAAkB,CAAC,QAAQ,EAAC,sBAAqB,GAAG,eAAe,UAAU,MAAM,KAAK,0BAA0B,SAAS;AAEjI,MAAM,YAAY,YAAU,WAAW,eAAAA,QAAG,UAAU,QAAQ,WACtD,OAAO,WAAW,YAAY,OAAO,YAAY,MAAM;AAE7D,MAAM,2BAA2B,CAAC,EAAC,wBAAwB,KAAI,MAAM;AACpE,MAAI,0BAA0B,MAAM;AACnC,WAAO;AAAA,EACR;AAEA,MAAI,CAAC,OAAO,SAAS,qBAAqB,KAAK,wBAAwB,GAAG;AACzE,UAAM,IAAI,UAAU,qFAAqF,4BAA4B,OAAO,wBAAwB;AAAA,EACrK;AAEA,SAAO;AACR;AAGO,MAAM,gBAAgB,CAAC,SAAS,YAAY;AAClD,QAAM,aAAa,QAAQ,KAAK;AAEhC,MAAI,YAAY;AACf,YAAQ,aAAa;AAAA,EACtB;AACD;AAEA,MAAM,cAAc,CAAC,SAAS,QAAQ,WAAW;AAChD,UAAQ,KAAK,MAAM;AACnB,SAAO,OAAO,OAAO,IAAI,MAAM,WAAW,GAAG,EAAC,UAAU,MAAM,OAAM,CAAC,CAAC;AACvE;AAGO,MAAM,eAAe,CAAC,SAAS,EAAC,SAAS,aAAa,UAAS,GAAG,mBAAmB;AAC3F,MAAI,YAAY,KAAK,YAAY,QAAW;AAC3C,WAAO;AAAA,EACR;AAEA,MAAI;AACJ,QAAM,iBAAiB,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvD,gBAAY,WAAW,MAAM;AAC5B,kBAAY,SAAS,YAAY,MAAM;AAAA,IACxC,GAAG,OAAO;AAAA,EACX,CAAC;AAED,QAAM,qBAAqB,eAAe,QAAQ,MAAM;AACvD,iBAAa,SAAS;AAAA,EACvB,CAAC;AAED,SAAO,QAAQ,KAAK,CAAC,gBAAgB,kBAAkB,CAAC;AACzD;AAEO,MAAM,kBAAkB,CAAC,EAAC,QAAO,MAAM;AAC7C,MAAI,YAAY,WAAc,CAAC,OAAO,SAAS,OAAO,KAAK,UAAU,IAAI;AACxE,UAAM,IAAI,UAAU,uEAAuE,cAAc,OAAO,UAAU;AAAA,EAC3H;AACD;AAGO,MAAM,iBAAiB,OAAO,SAAS,EAAC,SAAS,SAAQ,GAAG,iBAAiB;AACnF,MAAI,CAAC,WAAW,UAAU;AACzB,WAAO;AAAA,EACR;AAEA,QAAM,wBAAoB,mBAAAC,SAAO,MAAM;AACtC,YAAQ,KAAK;AAAA,EACd,CAAC;AAED,SAAO,aAAa,QAAQ,MAAM;AACjC,sBAAkB;AAAA,EACnB,CAAC;AACF;", "names": ["os", "onExit"]}