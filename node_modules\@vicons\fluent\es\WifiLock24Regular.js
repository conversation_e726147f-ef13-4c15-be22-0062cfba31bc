import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6.5 12a5.5 5.5 0 1 1 0 11a5.5 5.5 0 0 1 0-11zm0 1.998a1.75 1.75 0 0 0-1.744 1.607l-.006.143v.781c-.395.102-.696.44-.743.854L4 17.498v2.5a1 1 0 0 0 .883.993l.117.007h3a1 1 0 0 0 .993-.883L9 19.998v-2.5a1 1 0 0 0-.644-.935l-.105-.033l-.001-.782a1.75 1.75 0 0 0-1.75-1.75zm0 1a.75.75 0 0 1 .743.648l.007.102v.75h-1.5v-.75a.75.75 0 0 1 .75-.75zm9.143-1.179c.448.448.816.998 1.072 1.583a.75.75 0 1 1-1.373.602a3.583 3.583 0 0 0-3.475-2.171a6.52 6.52 0 0 0-1.195-1.318a5.09 5.09 0 0 1 4.971 1.304zm2.102-3.068a8.291 8.291 0 0 1 1.492 2.069a.75.75 0 1 1-1.335.683a6.798 6.798 0 0 0-1.218-1.692a6.563 6.563 0 0 0-8.576-.611a6.544 6.544 0 0 0-2.015-.188a8.062 8.062 0 0 1 11.652-.262zm2.703-2.472c.51.509.99 1.09 1.408 1.697a.75.75 0 0 1-1.234.852a10.822 10.822 0 0 0-1.234-1.489c-4.08-4.08-10.696-4.08-14.776 0c-.421.422-.84.934-1.222 1.484a.75.75 0 0 1-1.232-.855c.43-.62.904-1.2 1.394-1.69c4.666-4.665 12.23-4.665 16.896 0z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WifiLock24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
