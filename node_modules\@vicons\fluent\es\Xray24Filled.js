import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M14 16.75a.75.75 0 1 1 1.5 0a.75.75 0 0 1-1.5 0zM9.25 16a.75.75 0 1 0 0 1.5a.75.75 0 0 0 0-1.5zM4 5.25A3.25 3.25 0 0 1 7.25 2h9.5A3.25 3.25 0 0 1 20 5.25v13.5A3.25 3.25 0 0 1 16.75 22h-9.5A3.25 3.25 0 0 1 4 18.75V5.25zm8.75.5a.75.75 0 0 0-1.5 0v.75H9A.75.75 0 0 0 9 8h2.25v1h-3.5a.75.75 0 0 0 0 1.5h3.5v1h-3.5a.75.75 0 0 0 0 1.5h3.5v1.5h-2a2.25 2.25 0 1 0 2.122 1.5h1.256a2.25 2.25 0 1 0 2.122-1.5h-2V13h3.5a.75.75 0 0 0 0-1.5h-3.5v-1h3.5a.75.75 0 0 0 0-1.5h-3.5V8H15a.75.75 0 0 0 0-1.5h-2.25v-.75z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Xray24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
