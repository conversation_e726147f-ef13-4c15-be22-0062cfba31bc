import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M12.25 6A6.25 6.25 0 0 0 6 12.25v23.5A6.25 6.25 0 0 0 12.25 42H18v-2.5h-5.75a3.75 3.75 0 0 1-3.75-3.75V16.5h31V18H42v-5.75A6.25 6.25 0 0 0 35.75 6h-23.5zm27.25 8h-31v-1.75a3.75 3.75 0 0 1 3.75-3.75h23.5a3.75 3.75 0 0 1 3.75 3.75V14zm-4 6a3.75 3.75 0 0 0-3.75 3.75v8h-8A3.75 3.75 0 0 0 20 35.5v6.75A3.75 3.75 0 0 0 23.75 46h16A6.25 6.25 0 0 0 46 39.75v-16A3.75 3.75 0 0 0 42.25 20H35.5zm-1.25 3.75c0-.69.56-1.25 1.25-1.25h6.75c.69 0 1.25.56 1.25 1.25v8h-9.25v-8zm9.25 10.5v5.5a3.75 3.75 0 0 1-3.75 3.75h-5.5v-9.25h9.25zm-19.75 0h8v9.25h-8c-.69 0-1.25-.56-1.25-1.25V35.5c0-.69.56-1.25 1.25-1.25z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowApps48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
