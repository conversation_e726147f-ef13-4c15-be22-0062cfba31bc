import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M16.75 2.001a5.25 5.25 0 0 0-5.005 6.839l-9.068 9.38a2.344 2.344 0 1 0 3.37 3.258l8.963-9.272a5.25 5.25 0 0 0 6.786-6.407a.75.75 0 0 0-1.251-.323L17.361 8.66l-2.06-2.06l3.16-3.162a.75.75 0 0 0-.333-1.254A5.255 5.255 0 0 0 16.75 2z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Wrench24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
