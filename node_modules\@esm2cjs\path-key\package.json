{"name": "@esm2cjs/path-key", "version": "4.0.0", "description": "Get the PATH environment variable key cross-platform. This is a fork of sindresorhus/path-key, but with CommonJS support.", "license": "MIT", "repository": "esm2cjs/path-key", "funding": "https://github.com/sponsors/AlCalzone", "author": {"name": "<PERSON>", "email": "d.gries<PERSON>@gmx.net"}, "exports": {".": {"import": "./esm/index.js", "require": "./cjs/index.js"}, "./package.json": "./package.json"}, "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["cjs/", "esm/"], "keywords": ["path", "key", "environment", "env", "variable", "get", "cross-platform", "windows"], "devDependencies": {"@types/node": "^14.14.37", "ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "publishConfig": {"access": "public"}, "main": "cjs/index.js", "module": "esm/index.js", "types": "esm/index.d.ts", "typesVersions": {"*": {"esm/index.d.ts": ["esm/index.d.ts"], "cjs/index.d.ts": ["esm/index.d.ts"], "*": ["esm/*"]}}, "xo": {"ignores": ["cjs", "**/*.test-d.ts"]}}