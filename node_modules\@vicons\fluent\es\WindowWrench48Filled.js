import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M12.25 6A6.25 6.25 0 0 0 6 12.25v23.5A6.25 6.25 0 0 0 12.25 42h9.773a5.48 5.48 0 0 1 .866-2.5H12.25a3.75 3.75 0 0 1-3.75-3.75V16.5h31v5.612c.272.041.541.093.806.156c.865.204 1.426.75 1.694 1.407V12.25A6.25 6.25 0 0 0 35.75 6h-23.5zm17.725 38.975a3.5 3.5 0 0 1-4.95-4.95l5.412-5.411a8 8 0 0 1 9.41-10.4c.358.085.462.525.203.786l-.002.002l-3.523 3.523a3.5 3.5 0 0 0 4.95 4.95l3.523-3.523c.261-.262.703-.158.788.202a8 8 0 0 1-10.4 9.41l-5.411 5.41z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowWrench48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
