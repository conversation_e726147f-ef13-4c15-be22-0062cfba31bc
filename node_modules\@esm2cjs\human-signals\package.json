{"name": "@esm2cjs/human-signals", "version": "3.0.1", "exports": {".": {"import": "./build/esm/main.js", "require": "./build/cjs/main.js"}, "./package.json": "./package.json"}, "main": "build/cjs/main.js", "files": ["build/esm/**/*.{js,d.ts,json}", "build/cjs/**/*.{js,d.ts,json}"], "scripts": {"test": "gulp test"}, "description": "Human-friendly process signals. This is a fork of ehmicky/human-signals, but with CommonJS support.", "keywords": ["signal", "signals", "handlers", "error-handling", "errors", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "linux", "macos", "windows", "nodejs"], "license": "Apache-2.0", "repository": "esm2cjs/human-signals", "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "author": {"name": "<PERSON>", "email": "d.gries<PERSON>@gmx.net"}, "directories": {"lib": "src", "test": "test"}, "types": "build/esm/main.d.ts", "devDependencies": {"@ehmicky/dev-tasks": "^1.0.34", "ajv": "^6.12.6", "test-each": "^3.0.1"}, "engines": {"node": ">=12.20.0"}, "publishConfig": {"access": "public"}, "funding": "https://github.com/sponsors/AlCalzone", "typesVersions": {"*": {"build/esm/main.d.ts": ["build/esm/main.d.ts"], "build/cjs/main.d.ts": ["build/esm/main.d.ts"], "*": ["build/esm/*"]}}, "module": "build/esm/main.js"}