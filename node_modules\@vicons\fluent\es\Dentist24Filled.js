import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4.355 4.387C5.355 3.552 6.644 3 7.75 3c.869 0 1.551.138 2.107.37c.56.235.952.548 1.255.843c.132.13.25.258.347.364l.03.033c.11.12.187.202.26.267c.124.11.18.123.251.123c.071 0 .127-.013.25-.123c.074-.065.152-.147.261-.267l.03-.033c.098-.106.215-.235.347-.364a3.923 3.923 0 0 1 1.255-.842C14.699 3.138 15.38 3 16.25 3c1.106 0 2.395.552 3.395 1.387c1.006.84 1.855 2.081 1.855 3.561c0 .778-.002 2.306-1.373 3.938c-.37.585-.624 1.275-.627 1.868c-.01 1.926-.437 3.553-1.083 4.725c-.616 1.12-1.556 2.021-2.667 2.021c-.358 0-.691-.1-.976-.31a1.908 1.908 0 0 1-.58-.74c-.239-.512-.343-1.174-.418-1.803a51.664 51.664 0 0 1-.086-.798c-.115-1.109-.234-2.26-.627-3.296c-.186-.357-.506-.553-.813-.553h-.5c-.314 0-.643.205-.826.58c-.544 1.11-.611 2.363-.676 3.565l-.026.468c-.037.615-.095 1.296-.33 1.826a1.78 1.78 0 0 1-.62.769a1.785 1.785 0 0 1-1.022.292c-1.24 0-2.189-.871-2.79-2.04c-.607-1.18-.95-2.806-.96-4.706c-.003-.532-.207-1.145-.524-1.697a2.51 2.51 0 0 0-.262-.346C2.5 10.27 2.5 8.68 2.5 7.966v-.018c0-1.48.849-2.721 1.855-3.561zM15.75 6a.75.75 0 0 0 0 1.5c.714 0 1.25.536 1.25 1.25a.75.75 0 0 0 1.5 0A2.72 2.72 0 0 0 15.75 6z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Dentist24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
