import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2 4.501a2.5 2.5 0 0 1 2.5-2.5h9a2.5 2.5 0 0 1 2.5 2.5v2.211a3.801 3.801 0 0 0-1 .213V6H3V13.5a1.5 1.5 0 0 0 1.5 1.5h6.69l-.438.76a2.171 2.171 0 0 0-.12.24H4.5a2.5 2.5 0 0 1-2.5-2.5v-9zM4.499 3a1.5 1.5 0 0 0-1.5 1.5V5h12v-.5a1.5 1.5 0 0 0-1.5-1.5h-9zm9.375 6.091a2.78 2.78 0 0 0 .147 3.009l-2.403 4.161a1.158 1.158 0 0 0 2.006 1.158l2.407-4.17a2.78 2.78 0 0 0 2.363-4.575l-1.31 2.27a.927.927 0 0 1-1.605-.926l1.31-2.27a2.78 2.78 0 0 0-2.915 1.343zm-1.314 2.054a3.762 3.762 0 0 1 .215-2.076l-1.923-1.923a.5.5 0 1 0-.707.707L12.292 10l-2.147 2.146a.5.5 0 1 0 .707.707l1.708-1.708zM8.352 7.853a.5.5 0 0 0-.707-.707l-2.5 2.5a.5.5 0 0 0 0 .707l2.5 2.5a.5.5 0 0 0 .707-.707L6.206 10l2.146-2.147z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowDevTools20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
