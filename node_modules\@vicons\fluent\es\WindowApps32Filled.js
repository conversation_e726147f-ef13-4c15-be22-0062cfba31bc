import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3 7.5A4.5 4.5 0 0 1 7.5 3h17A4.5 4.5 0 0 1 29 7.5v4.111a4.513 4.513 0 0 0-1-.111h-1V11H5v13.5A2.5 2.5 0 0 0 7.5 27h4v1c0 .344.039.678.111 1H7.5A4.5 4.5 0 0 1 3 24.5v-17zM24 13a3 3 0 0 0-3 3v5h-5a3 3 0 0 0-3 3v4a3 3 0 0 0 3 3h10.5a4.5 4.5 0 0 0 4.5-4.5V16a3 3 0 0 0-3-3h-4zm-1 3a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v5h-6v-5zm-7 7h5v6h-5a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1zm10.5 6H23v-6h6v3.5a2.5 2.5 0 0 1-2.5 2.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowApps32Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
