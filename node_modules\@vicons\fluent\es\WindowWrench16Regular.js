import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2 4.5A2.5 2.5 0 0 1 4.5 2h7A2.5 2.5 0 0 1 14 4.5v1.124a1.383 1.383 0 0 0-.993-.594c-.174-.02-.349-.03-.524-.03H13v-.5A1.5 1.5 0 0 0 11.5 3h-7A1.5 1.5 0 0 0 3 4.5V5h9.483c-1.004 0-2.01.333-2.836 1H3v5.5A1.5 1.5 0 0 0 4.5 13h.992a2.493 2.493 0 0 0-.443 1H4.5A2.5 2.5 0 0 1 2 11.5v-7zm11.242 1.861a.387.387 0 0 0-.35-.337A3.518 3.518 0 0 0 9.169 10.7l-2.727 2.726a1.508 1.508 0 0 0 2.132 2.132l2.726-2.726a3.518 3.518 0 0 0 4.676-3.724c-.042-.366-.48-.471-.742-.21l-.975.975a1.507 1.507 0 1 1-2.132-2.132l.975-.975a.489.489 0 0 0 .14-.405z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowWrench16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
