import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M8.94 3.929c-.936-.757-1.975-.918-2.905-.736A4.259 4.259 0 0 0 3.777 4.46c-.603.656-1.194 1.486-1.241 2.7c-.022.602.067 1.475.532 2.129l.002.002c.613.851.922 1.475 1.048 2.287c.04.26.063.543.068.867c.005.644.14 1.529.466 2.334c.323.795.873 1.608 1.77 1.939l.022.008l.023.006c.524.14 1.012.09 1.38-.255c.324-.303.46-.754.535-1.16c.07-.376.105-.812.14-1.241l.012-.153c.04-.491.086-.98.181-1.442c.266-1.273.647-1.497.824-1.55c.491-.127.779-.041.97.098c.278.202.49.615.638 1.224c.143.593.2 1.264.25 1.877c.072.866.255 1.654.604 2.158c.18.262.437.49.784.565c.35.075.69-.03.99-.224c1.104-.704 1.743-2.167 1.918-4.105c.08-.871.169-1.294.298-1.628c.1-.26.223-.47.418-.802a37.592 37.592 0 0 0 .363-.632c.244-.436.645-1.23.69-2.16c.048-.966-.29-2.033-1.431-2.961c-1.09-.886-2.106-1.207-3.013-1.158c-.898.05-1.617.458-2.128.896l-.013.01l-.011.013c-.5.49-.73.577-.88.57c-.168-.006-.442-.137-1.005-.705l-.02-.02l-.021-.017zm3.995 1.669c.49 0 .987.148 1.382.498c.4.354.647.873.707 1.526a.5.5 0 0 1-.996.09c-.041-.445-.198-.712-.374-.868c-.18-.16-.428-.246-.719-.246a.5.5 0 0 1 0-1z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Dentist20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
