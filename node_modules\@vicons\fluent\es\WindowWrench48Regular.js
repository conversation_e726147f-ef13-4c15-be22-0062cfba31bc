import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6 12.25A6.25 6.25 0 0 1 12.25 6h23.5A6.25 6.25 0 0 1 42 12.25v11.425c-.268-.657-.83-1.203-1.694-1.407a9.957 9.957 0 0 0-.806-.156V16.5h-31v19.25a3.75 3.75 0 0 0 3.75 3.75h10.639a5.48 5.48 0 0 0-.866 2.5H12.25A6.25 6.25 0 0 1 6 35.75v-23.5zm33.5 0a3.75 3.75 0 0 0-3.75-3.75h-23.5a3.75 3.75 0 0 0-3.75 3.75V14h31v-1.75zm.346 11.964c.359.085.463.525.204.786l-.002.002l-3.523 3.523a3.5 3.5 0 0 0 4.95 4.95l3.523-3.523c.261-.262.703-.158.788.202a8 8 0 0 1-10.4 9.41l-5.411 5.41a3.5 3.5 0 0 1-4.95-4.949l5.412-5.411a8 8 0 0 1 9.41-10.4z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowWrench48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
