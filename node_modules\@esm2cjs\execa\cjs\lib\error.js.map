{"version": 3, "sources": ["../../esm/lib/error.js"], "sourcesContent": ["import {signalsByName} from '@esm2cjs/human-signals';\n\nconst getErrorPrefix = ({timedOut, timeout, errorCode, signal, signalDescription, exitCode, isCanceled}) => {\n\tif (timedOut) {\n\t\treturn `timed out after ${timeout} milliseconds`;\n\t}\n\n\tif (isCanceled) {\n\t\treturn 'was canceled';\n\t}\n\n\tif (errorCode !== undefined) {\n\t\treturn `failed with ${errorCode}`;\n\t}\n\n\tif (signal !== undefined) {\n\t\treturn `was killed with ${signal} (${signalDescription})`;\n\t}\n\n\tif (exitCode !== undefined) {\n\t\treturn `failed with exit code ${exitCode}`;\n\t}\n\n\treturn 'failed';\n};\n\nexport const makeError = ({\n\tstdout,\n\tstderr,\n\tall,\n\terror,\n\tsignal,\n\texitCode,\n\tcommand,\n\tescapedCommand,\n\ttimedOut,\n\tisCanceled,\n\tkilled,\n\tparsed: {options: {timeout}},\n}) => {\n\t// `signal` and `exitCode` emitted on `spawned.on('exit')` event can be `null`.\n\t// We normalize them to `undefined`\n\texitCode = exitCode === null ? undefined : exitCode;\n\tsignal = signal === null ? undefined : signal;\n\tconst signalDescription = signal === undefined ? undefined : signalsByName[signal].description;\n\n\tconst errorCode = error && error.code;\n\n\tconst prefix = getErrorPrefix({timedOut, timeout, errorCode, signal, signalDescription, exitCode, isCanceled});\n\tconst execaMessage = `Command ${prefix}: ${command}`;\n\tconst isError = Object.prototype.toString.call(error) === '[object Error]';\n\tconst shortMessage = isError ? `${execaMessage}\\n${error.message}` : execaMessage;\n\tconst message = [shortMessage, stderr, stdout].filter(Boolean).join('\\n');\n\n\tif (isError) {\n\t\terror.originalMessage = error.message;\n\t\terror.message = message;\n\t} else {\n\t\terror = new Error(message);\n\t}\n\n\terror.shortMessage = shortMessage;\n\terror.command = command;\n\terror.escapedCommand = escapedCommand;\n\terror.exitCode = exitCode;\n\terror.signal = signal;\n\terror.signalDescription = signalDescription;\n\terror.stdout = stdout;\n\terror.stderr = stderr;\n\n\tif (all !== undefined) {\n\t\terror.all = all;\n\t}\n\n\tif ('bufferedData' in error) {\n\t\tdelete error.bufferedData;\n\t}\n\n\terror.failed = true;\n\terror.timedOut = Boolean(timedOut);\n\terror.isCanceled = isCanceled;\n\terror.killed = killed && !timedOut;\n\n\treturn error;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAA4B;AAE5B,MAAM,iBAAiB,CAAC,EAAC,UAAU,SAAS,WAAW,QAAQ,mBAAmB,UAAU,WAAU,MAAM;AAC3G,MAAI,UAAU;AACb,WAAO,mBAAmB;AAAA,EAC3B;AAEA,MAAI,YAAY;AACf,WAAO;AAAA,EACR;AAEA,MAAI,cAAc,QAAW;AAC5B,WAAO,eAAe;AAAA,EACvB;AAEA,MAAI,WAAW,QAAW;AACzB,WAAO,mBAAmB,WAAW;AAAA,EACtC;AAEA,MAAI,aAAa,QAAW;AAC3B,WAAO,yBAAyB;AAAA,EACjC;AAEA,SAAO;AACR;AAEO,MAAM,YAAY,CAAC;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ,EAAC,SAAS,EAAC,QAAO,EAAC;AAC5B,MAAM;AAGL,aAAW,aAAa,OAAO,SAAY;AAC3C,WAAS,WAAW,OAAO,SAAY;AACvC,QAAM,oBAAoB,WAAW,SAAY,SAAY,mCAAc,QAAQ;AAEnF,QAAM,YAAY,SAAS,MAAM;AAEjC,QAAM,SAAS,eAAe,EAAC,UAAU,SAAS,WAAW,QAAQ,mBAAmB,UAAU,WAAU,CAAC;AAC7G,QAAM,eAAe,WAAW,WAAW;AAC3C,QAAM,UAAU,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AAC1D,QAAM,eAAe,UAAU,GAAG;AAAA,EAAiB,MAAM,YAAY;AACrE,QAAM,UAAU,CAAC,cAAc,QAAQ,MAAM,EAAE,OAAO,OAAO,EAAE,KAAK,IAAI;AAExE,MAAI,SAAS;AACZ,UAAM,kBAAkB,MAAM;AAC9B,UAAM,UAAU;AAAA,EACjB,OAAO;AACN,YAAQ,IAAI,MAAM,OAAO;AAAA,EAC1B;AAEA,QAAM,eAAe;AACrB,QAAM,UAAU;AAChB,QAAM,iBAAiB;AACvB,QAAM,WAAW;AACjB,QAAM,SAAS;AACf,QAAM,oBAAoB;AAC1B,QAAM,SAAS;AACf,QAAM,SAAS;AAEf,MAAI,QAAQ,QAAW;AACtB,UAAM,MAAM;AAAA,EACb;AAEA,MAAI,kBAAkB,OAAO;AAC5B,WAAO,MAAM;AAAA,EACd;AAEA,QAAM,SAAS;AACf,QAAM,WAAW,QAAQ,QAAQ;AACjC,QAAM,aAAa;AACnB,QAAM,SAAS,UAAU,CAAC;AAE1B,SAAO;AACR;", "names": []}