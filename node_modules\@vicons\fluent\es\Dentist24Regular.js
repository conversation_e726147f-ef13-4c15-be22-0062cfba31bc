import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M15.75 6a.75.75 0 0 0 0 1.5c.714 0 1.25.536 1.25 1.25a.75.75 0 0 0 1.5 0A2.72 2.72 0 0 0 15.75 6zm-8-3c-1.106 0-2.395.552-3.395 1.387C3.349 5.227 2.5 6.468 2.5 7.948v.018c0 .715 0 2.304 1.214 3.745c.128.152.206.249.262.346c.317.552.521 1.165.524 1.697c.01 1.9.353 3.526.96 4.707c.601 1.168 1.55 2.039 2.79 2.039c.367 0 .718-.086 1.021-.292c.3-.204.494-.484.62-.769c.236-.53.293-1.211.33-1.826c.01-.155.019-.31.027-.468c.065-1.202.132-2.455.676-3.565c.183-.375.512-.58.826-.58h.5c.307 0 .627.196.813.553c.393 1.036.512 2.187.627 3.296c.028.27.055.536.087.798c.075.63.178 1.29.418 1.802c.125.268.306.538.58.74c.284.21.617.311.975.311c1.11 0 2.051-.902 2.667-2.02c.646-1.173 1.072-2.8 1.083-4.726c.003-.593.257-1.283.627-1.868c1.37-1.632 1.373-3.16 1.373-3.938c0-1.48-.849-2.721-1.855-3.561C18.645 3.552 17.356 3 16.25 3c-.869 0-1.551.138-2.107.37c-.56.235-.952.548-1.255.843c-.132.13-.25.258-.347.364l-.03.033c-.11.12-.187.202-.26.267c-.124.11-.18.123-.251.123c-.071 0-.127-.013-.25-.123a3.826 3.826 0 0 1-.261-.267l-.03-.033c-.098-.106-.215-.235-.347-.364a3.923 3.923 0 0 0-1.255-.842C9.301 3.138 8.62 3 7.75 3zM4 7.948c0-.884.515-1.741 1.316-2.41C6.123 4.865 7.084 4.5 7.75 4.5c.708 0 1.188.112 1.529.254c.337.141.575.328.785.533c.1.098.19.196.29.306l.03.031c.105.115.23.251.369.374c.297.265.692.502 1.247.502c.555 0 .95-.237 1.247-.502c.138-.123.264-.259.37-.374l.028-.031c.1-.11.191-.208.291-.306c.21-.205.448-.392.785-.533c.341-.142.821-.254 1.529-.254c.666 0 1.627.365 2.434 1.038c.8.669 1.316 1.526 1.316 2.41v.004c0 .68 0 1.772-1.049 3.001a.754.754 0 0 0-.059.08c-.48.743-.887 1.728-.892 2.713c-.01 1.72-.392 3.094-.897 4.01C16.57 18.726 16.02 19 15.75 19c-.063 0-.079-.013-.082-.016a.452.452 0 0 1-.115-.17c-.124-.267-.21-.702-.287-1.345c-.025-.213-.05-.445-.074-.692c-.115-1.126-.259-2.545-.742-3.797a.775.775 0 0 0-.026-.06c-.389-.793-1.188-1.42-2.174-1.42h-.5c-.986 0-1.785.627-2.174 1.42c-.689 1.407-.768 2.987-.828 4.161l-.023.442c-.04.654-.096 1.064-.205 1.307c-.045.103-.081.13-.091.137c-.007.005-.049.033-.179.033c-.441 0-.982-.305-1.456-1.226c-.467-.907-.784-2.281-.794-4.028c-.005-.872-.324-1.74-.723-2.436a3.813 3.813 0 0 0-.407-.555l-.01-.01C4.01 9.734 4 8.624 4 7.947z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Dentist24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
