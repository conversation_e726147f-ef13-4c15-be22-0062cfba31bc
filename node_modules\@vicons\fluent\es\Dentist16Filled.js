import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4.503 2.141a3.108 3.108 0 0 1 2.604.656l.023.017l.02.02c.495.501.721.6.843.605c.1.004.285-.053.723-.49l.014-.015l.016-.014c.46-.389 1.106-.753 1.913-.796c.815-.042 1.721.247 2.687 1.03c1.015.823 1.318 1.778 1.275 2.645c-.04.833-.397 1.543-.613 1.93l-.001.002c-.116.206-.221.384-.31.534c-.04.065-.075.125-.107.18c-.11.19-.188.333-.25.483c-.12.292-.202.654-.273 1.464c-.15 1.71-.72 3.021-1.714 3.656c-.273.174-.588.271-.915.2c-.325-.069-.561-.281-.726-.52c-.315-.455-.474-1.157-.534-1.92c-.07-.768-.146-1.598-.404-2.182c-.124-.282-.269-.453-.425-.542c-.144-.082-.365-.132-.736-.037c-.125.039-.45.212-.676 1.315v.003c-.085.402-.126.83-.162 1.262l-.011.141c-.03.374-.062.759-.124 1.09c-.067.358-.189.772-.489 1.053c-.343.321-.796.363-1.269.237l-.021-.006l-.021-.007c-.816-.297-1.311-1.031-1.599-1.737a5.934 5.934 0 0 1-.417-2.073V10.277c0-.59 0-.984-.143-1.376c-.125-.343-.374-.697-.82-1.302l-.006-.007c-.422-.596-.498-1.375-.477-1.91c.046-1.089.58-1.834 1.104-2.408l.002-.003c.48-.518 1.201-.97 2.019-1.13zm6.08 2.098a.5.5 0 1 0 0 1c.242 0 .442.072.587.2c.142.127.275.347.313.728a.5.5 0 0 0 .995-.1c-.058-.583-.282-1.052-.643-1.374a1.86 1.86 0 0 0-1.252-.454z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Dentist16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
