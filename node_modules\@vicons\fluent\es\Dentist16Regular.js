import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M10.583 4.239a.5.5 0 1 0 0 1c.242 0 .442.072.587.2c.142.127.275.347.313.728a.5.5 0 0 0 .995-.1c-.058-.583-.282-1.052-.643-1.374a1.86 1.86 0 0 0-1.252-.454zM7.107 2.797a3.108 3.108 0 0 0-2.604-.656c-.818.16-1.54.612-2.019 1.13l-.002.003c-.523.574-1.058 1.32-1.104 2.407v.002c-.02.534.056 1.313.477 1.909l.006.007c.446.605.695.959.82 1.301c.143.392.143.788.143 1.377V10.325c.005.57.126 1.357.417 2.073c.288.706.783 1.44 1.599 1.737l.02.007l.022.006c.473.126.926.084 1.27-.237c.3-.28.42-.694.488-1.053c.062-.331.093-.716.124-1.09l.011-.141c.036-.432.077-.86.161-1.262v-.003c.227-1.103.552-1.276.677-1.315c.37-.095.592-.045.736.037c.156.089.3.26.425.542c.258.585.334 1.414.404 2.182c.06.763.22 1.465.534 1.92c.165.239.402.451.726.52c.327.071.642-.026.915-.2c.994-.635 1.564-1.946 1.714-3.655c.07-.811.152-1.173.273-1.465c.062-.15.14-.294.25-.483l.106-.18c.09-.15.195-.328.31-.534l.002-.001c.216-.388.572-1.098.613-1.93c.043-.868-.26-1.823-1.275-2.645c-.966-.784-1.872-1.073-2.687-1.03c-.807.042-1.452.406-1.913.795l-.016.014l-.014.014c-.438.438-.623.495-.723.49c-.122-.005-.348-.103-.844-.604l-.02-.02l-.022-.017zm-4.73 2.926c.032-.743.383-1.27.843-1.774c.35-.378.886-.711 1.474-.826a2.106 2.106 0 0 1 1.766.436c.47.473.942.855 1.49.879c.57.024 1.032-.344 1.457-.767c.347-.29.786-.52 1.304-.548c.518-.027 1.188.146 2.005.808c.759.615.934 1.26.906 1.82c-.029.593-.29 1.136-.487 1.49a23.412 23.412 0 0 1-.409.7c-.116.2-.221.39-.31.604c-.18.436-.272.922-.346 1.76c-.139 1.587-.649 2.513-1.255 2.9a.461.461 0 0 1-.134.063c-.023.005-.032.003-.033.003c-.001 0-.01-.002-.028-.015a.417.417 0 0 1-.085-.096c-.16-.23-.304-.712-.36-1.433v-.006l-.007-.075c-.064-.71-.152-1.682-.479-2.423c-.173-.393-.435-.775-.846-1.009c-.422-.24-.926-.28-1.49-.132l-.008.002l-.007.002c-.67.195-1.128.844-1.38 2.075c-.099.469-.144.952-.18 1.383l-.01.14a10.25 10.25 0 0 1-.111.99c-.06.324-.137.458-.189.506v.001c-.011.01-.066.066-.307.007c-.418-.162-.755-.581-.993-1.167a4.938 4.938 0 0 1-.344-1.704v-.103c0-.532.002-1.093-.204-1.656c-.183-.504-.531-.979-.951-1.548c-.232-.33-.31-.84-.292-1.287z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Dentist16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
