import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M17.744 10.75c.099.099.195.201.289.306a2.536 2.536 0 0 0-1.843.309a6.563 6.563 0 0 0-10 2.12a.75.75 0 1 1-1.339-.678a8.062 8.062 0 0 1 12.893-2.057zM11.957 19l1.216-2.434A1.5 1.5 0 1 0 11.957 19zm2.23-4.462l.677-1.357a5.094 5.094 0 0 0-6.422.638a5.11 5.11 0 0 0-1.061 1.57a.75.75 0 0 0 1.374.602c.185-.422.437-.8.747-1.11a3.593 3.593 0 0 1 4.684-.343zm6.26-6.26c.51.51.99 1.091 1.409 1.698a.75.75 0 1 1-1.235.852a10.822 10.822 0 0 0-1.234-1.489c-4.08-4.08-10.695-4.08-14.775 0c-.422.422-.84.934-1.222 1.484a.75.75 0 0 1-1.233-.855c.43-.62.905-1.2 1.394-1.69c4.666-4.665 12.23-4.665 16.897 0zm-4.29 4.552l-3.997 7.998A1.5 1.5 0 0 0 13.503 23h7.994a1.5 1.5 0 0 0 1.343-2.172l-3.997-7.998c-.553-1.107-2.133-1.107-2.686 0zM18 15.496v3.001a.5.5 0 0 1-1 0v-3.001a.5.5 0 0 1 1 0zm-.5 5.503a.5.5 0 1 1 0-1.001a.5.5 0 0 1 0 1z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WifiWarning24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
