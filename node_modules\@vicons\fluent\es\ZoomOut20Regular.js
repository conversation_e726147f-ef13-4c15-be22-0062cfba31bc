import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M11 8a.5.5 0 0 1 0 1H6a.5.5 0 0 1 0-1h5zm3 .5a5.5 5.5 0 1 0-1.98 4.227l4.126 4.127l.07.057a.5.5 0 0 0 .638-.765l-4.127-4.126A5.478 5.478 0 0 0 14 8.5zm-10 0a4.5 4.5 0 1 1 9 0a4.5 4.5 0 0 1-9 0z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'ZoomOut20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
