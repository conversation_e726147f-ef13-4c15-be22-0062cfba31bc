import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2 4.75A2.75 2.75 0 0 1 4.75 2h6.5A2.75 2.75 0 0 1 14 4.75v1.667c-.506-.134-1.002-.438-1.5-.967v-.451h-9v6.251c0 .69.56 1.25 1.25 1.25h2.644a5.388 5.388 0 0 0 .842 1.5H4.75A2.75 2.75 0 0 1 2 11.25v-6.5zm10.5 2.032c.473.343.973.562 1.5.66c.212.038.429.058.65.058c.17 0 .31.129.343.3l.007.075v2.25c0 2.415-1.149 4.056-3.39 4.856a.329.329 0 0 1-.22 0c-.693-.248-1.281-.575-1.763-.981a4.318 4.318 0 0 1-1.15-1.5c-.289-.62-.447-1.333-.473-2.135L8 10.125v-2.25c0-.207.157-.375.35-.375c1.07 0 2.033-.458 2.903-1.39a.334.334 0 0 1 .495 0c.243.261.494.485.752.672z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowShield16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
