{"version": 3, "sources": ["../../esm/lib/stream.js"], "sourcesContent": ["import {isStream} from '@esm2cjs/is-stream';\nimport getStream from 'get-stream';\nimport mergeStream from 'merge-stream';\n\n// `input` option\nexport const handleInput = (spawned, input) => {\n\t// Checking for stdin is workaround for https://github.com/nodejs/node/issues/26852\n\t// @todo remove `|| spawned.stdin === undefined` once we drop support for Node.js <=12.2.0\n\tif (input === undefined || spawned.stdin === undefined) {\n\t\treturn;\n\t}\n\n\tif (isStream(input)) {\n\t\tinput.pipe(spawned.stdin);\n\t} else {\n\t\tspawned.stdin.end(input);\n\t}\n};\n\n// `all` interleaves `stdout` and `stderr`\nexport const makeAllStream = (spawned, {all}) => {\n\tif (!all || (!spawned.stdout && !spawned.stderr)) {\n\t\treturn;\n\t}\n\n\tconst mixed = mergeStream();\n\n\tif (spawned.stdout) {\n\t\tmixed.add(spawned.stdout);\n\t}\n\n\tif (spawned.stderr) {\n\t\tmixed.add(spawned.stderr);\n\t}\n\n\treturn mixed;\n};\n\n// On failure, `result.stdout|stderr|all` should contain the currently buffered stream\nconst getBufferedData = async (stream, streamPromise) => {\n\tif (!stream) {\n\t\treturn;\n\t}\n\n\tstream.destroy();\n\n\ttry {\n\t\treturn await streamPromise;\n\t} catch (error) {\n\t\treturn error.bufferedData;\n\t}\n};\n\nconst getStreamPromise = (stream, {encoding, buffer, maxBuffer}) => {\n\tif (!stream || !buffer) {\n\t\treturn;\n\t}\n\n\tif (encoding) {\n\t\treturn getStream(stream, {encoding, maxBuffer});\n\t}\n\n\treturn getStream.buffer(stream, {maxBuffer});\n};\n\n// Retrieve result of child process: exit code, signal, error, streams (stdout/stderr/all)\nexport const getSpawnedResult = async ({stdout, stderr, all}, {encoding, buffer, maxBuffer}, processDone) => {\n\tconst stdoutPromise = getStreamPromise(stdout, {encoding, buffer, maxBuffer});\n\tconst stderrPromise = getStreamPromise(stderr, {encoding, buffer, maxBuffer});\n\tconst allPromise = getStreamPromise(all, {encoding, buffer, maxBuffer: maxBuffer * 2});\n\n\ttry {\n\t\treturn await Promise.all([processDone, stdoutPromise, stderrPromise, allPromise]);\n\t} catch (error) {\n\t\treturn Promise.all([\n\t\t\t{error, signal: error.signal, timedOut: error.timedOut},\n\t\t\tgetBufferedData(stdout, stdoutPromise),\n\t\t\tgetBufferedData(stderr, stderrPromise),\n\t\t\tgetBufferedData(all, allPromise),\n\t\t]);\n\t}\n};\n\nexport const validateInputSync = ({input}) => {\n\tif (isStream(input)) {\n\t\tthrow new TypeError('The `input` option cannot be a stream in sync mode');\n\t}\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAAuB;AACvB,wBAAsB;AACtB,0BAAwB;AAGjB,MAAM,cAAc,CAAC,SAAS,UAAU;AAG9C,MAAI,UAAU,UAAa,QAAQ,UAAU,QAAW;AACvD;AAAA,EACD;AAEA,UAAI,2BAAS,KAAK,GAAG;AACpB,UAAM,KAAK,QAAQ,KAAK;AAAA,EACzB,OAAO;AACN,YAAQ,MAAM,IAAI,KAAK;AAAA,EACxB;AACD;AAGO,MAAM,gBAAgB,CAAC,SAAS,EAAC,IAAG,MAAM;AAChD,MAAI,CAAC,OAAQ,CAAC,QAAQ,UAAU,CAAC,QAAQ,QAAS;AACjD;AAAA,EACD;AAEA,QAAM,YAAQ,oBAAAA,SAAY;AAE1B,MAAI,QAAQ,QAAQ;AACnB,UAAM,IAAI,QAAQ,MAAM;AAAA,EACzB;AAEA,MAAI,QAAQ,QAAQ;AACnB,UAAM,IAAI,QAAQ,MAAM;AAAA,EACzB;AAEA,SAAO;AACR;AAGA,MAAM,kBAAkB,OAAO,QAAQ,kBAAkB;AACxD,MAAI,CAAC,QAAQ;AACZ;AAAA,EACD;AAEA,SAAO,QAAQ;AAEf,MAAI;AACH,WAAO,MAAM;AAAA,EACd,SAAS,OAAP;AACD,WAAO,MAAM;AAAA,EACd;AACD;AAEA,MAAM,mBAAmB,CAAC,QAAQ,EAAC,UAAU,QAAQ,UAAS,MAAM;AACnE,MAAI,CAAC,UAAU,CAAC,QAAQ;AACvB;AAAA,EACD;AAEA,MAAI,UAAU;AACb,eAAO,kBAAAC,SAAU,QAAQ,EAAC,UAAU,UAAS,CAAC;AAAA,EAC/C;AAEA,SAAO,kBAAAA,QAAU,OAAO,QAAQ,EAAC,UAAS,CAAC;AAC5C;AAGO,MAAM,mBAAmB,OAAO,EAAC,QAAQ,QAAQ,IAAG,GAAG,EAAC,UAAU,QAAQ,UAAS,GAAG,gBAAgB;AAC5G,QAAM,gBAAgB,iBAAiB,QAAQ,EAAC,UAAU,QAAQ,UAAS,CAAC;AAC5E,QAAM,gBAAgB,iBAAiB,QAAQ,EAAC,UAAU,QAAQ,UAAS,CAAC;AAC5E,QAAM,aAAa,iBAAiB,KAAK,EAAC,UAAU,QAAQ,WAAW,YAAY,EAAC,CAAC;AAErF,MAAI;AACH,WAAO,MAAM,QAAQ,IAAI,CAAC,aAAa,eAAe,eAAe,UAAU,CAAC;AAAA,EACjF,SAAS,OAAP;AACD,WAAO,QAAQ,IAAI;AAAA,MAClB,EAAC,OAAO,QAAQ,MAAM,QAAQ,UAAU,MAAM,SAAQ;AAAA,MACtD,gBAAgB,QAAQ,aAAa;AAAA,MACrC,gBAAgB,QAAQ,aAAa;AAAA,MACrC,gBAAgB,KAAK,UAAU;AAAA,IAChC,CAAC;AAAA,EACF;AACD;AAEO,MAAM,oBAAoB,CAAC,EAAC,MAAK,MAAM;AAC7C,UAAI,2BAAS,KAAK,GAAG;AACpB,UAAM,IAAI,UAAU,oDAAoD;AAAA,EACzE;AACD;", "names": ["mergeStream", "getStream"]}