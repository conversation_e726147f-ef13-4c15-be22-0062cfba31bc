import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3 5.5A2.5 2.5 0 0 1 5.5 3h9A2.5 2.5 0 0 1 17 5.5V6H3.003v1H15a3.5 3.5 0 0 0-2.45 6c-.816 0-1.461.412-1.864.864A2.678 2.678 0 0 0 10 15.62v.32c0 .372.056.726.16 1.06H5.5A2.5 2.5 0 0 1 3 14.5v-9zM15 13a2.5 2.5 0 1 1 2-4h-2v1h2.45a2.512 2.512 0 0 1 0 1H15v1h2c-.456.607-1.182 1-2 1zm-2.45 1c-.465 0-.855.235-1.116.529c-.26.291-.434.686-.434 1.091v.32c0 1.634 1.633 3.06 4 3.06c1.24 0 2.28-.392 2.988-1H15v-1h3.761c.148-.315.23-.653.238-1H15v-1h3.867a1.779 1.779 0 0 0-.3-.471c-.262-.294-.652-.529-1.117-.529h-4.9z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowInprivateAccount20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
