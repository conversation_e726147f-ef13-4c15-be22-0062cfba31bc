{"version": 3, "sources": ["../../esm/lib/stdio.js"], "sourcesContent": ["const aliases = ['stdin', 'stdout', 'stderr'];\n\nconst hasAlias = options => aliases.some(alias => options[alias] !== undefined);\n\nexport const normalizeStdio = options => {\n\tif (!options) {\n\t\treturn;\n\t}\n\n\tconst {stdio} = options;\n\n\tif (stdio === undefined) {\n\t\treturn aliases.map(alias => options[alias]);\n\t}\n\n\tif (hasAlia<PERSON>(options)) {\n\t\tthrow new Error(`It's not possible to provide \\`stdio\\` in combination with one of ${aliases.map(alias => `\\`${alias}\\``).join(', ')}`);\n\t}\n\n\tif (typeof stdio === 'string') {\n\t\treturn stdio;\n\t}\n\n\tif (!Array.isArray(stdio)) {\n\t\tthrow new TypeError(`Expected \\`stdio\\` to be of type \\`string\\` or \\`Array\\`, got \\`${typeof stdio}\\``);\n\t}\n\n\tconst length = Math.max(stdio.length, aliases.length);\n\treturn Array.from({length}, (value, index) => stdio[index]);\n};\n\n// `ipc` is pushed unless it is already present\nexport const normalizeStdioNode = options => {\n\tconst stdio = normalizeStdio(options);\n\n\tif (stdio === 'ipc') {\n\t\treturn 'ipc';\n\t}\n\n\tif (stdio === undefined || typeof stdio === 'string') {\n\t\treturn [stdio, stdio, stdio, 'ipc'];\n\t}\n\n\tif (stdio.includes('ipc')) {\n\t\treturn stdio;\n\t}\n\n\treturn [...stdio, 'ipc'];\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAAM,UAAU,CAAC,SAAS,UAAU,QAAQ;AAE5C,MAAM,WAAW,aAAW,QAAQ,KAAK,WAAS,QAAQ,WAAW,MAAS;AAEvE,MAAM,iBAAiB,aAAW;AACxC,MAAI,CAAC,SAAS;AACb;AAAA,EACD;AAEA,QAAM,EAAC,MAAK,IAAI;AAEhB,MAAI,UAAU,QAAW;AACxB,WAAO,QAAQ,IAAI,WAAS,QAAQ,MAAM;AAAA,EAC3C;AAEA,MAAI,SAAS,OAAO,GAAG;AACtB,UAAM,IAAI,MAAM,qEAAqE,QAAQ,IAAI,WAAS,KAAK,SAAS,EAAE,KAAK,IAAI,GAAG;AAAA,EACvI;AAEA,MAAI,OAAO,UAAU,UAAU;AAC9B,WAAO;AAAA,EACR;AAEA,MAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AAC1B,UAAM,IAAI,UAAU,mEAAmE,OAAO,SAAS;AAAA,EACxG;AAEA,QAAM,SAAS,KAAK,IAAI,MAAM,QAAQ,QAAQ,MAAM;AACpD,SAAO,MAAM,KAAK,EAAC,OAAM,GAAG,CAAC,OAAO,UAAU,MAAM,MAAM;AAC3D;AAGO,MAAM,qBAAqB,aAAW;AAC5C,QAAM,QAAQ,eAAe,OAAO;AAEpC,MAAI,UAAU,OAAO;AACpB,WAAO;AAAA,EACR;AAEA,MAAI,UAAU,UAAa,OAAO,UAAU,UAAU;AACrD,WAAO,CAAC,OAAO,OAAO,OAAO,KAAK;AAAA,EACnC;AAEA,MAAI,MAAM,SAAS,KAAK,GAAG;AAC1B,WAAO;AAAA,EACR;AAEA,SAAO,CAAC,GAAG,OAAO,KAAK;AACxB;", "names": []}