{"version": 3, "sources": ["../../esm/lib/command.js"], "sourcesContent": ["const normalizeArgs = (file, args = []) => {\n\tif (!Array.isArray(args)) {\n\t\treturn [file];\n\t}\n\n\treturn [file, ...args];\n};\n\nconst NO_ESCAPE_REGEXP = /^[\\w.-]+$/;\nconst DOUBLE_QUOTES_REGEXP = /\"/g;\n\nconst escapeArg = arg => {\n\tif (typeof arg !== 'string' || NO_ESCAPE_REGEXP.test(arg)) {\n\t\treturn arg;\n\t}\n\n\treturn `\"${arg.replace(DOUBLE_QUOTES_REGEXP, '\\\\\"')}\"`;\n};\n\nexport const joinCommand = (file, args) => normalizeArgs(file, args).join(' ');\n\nexport const getEscapedCommand = (file, args) => normalizeArgs(file, args).map(arg => escapeArg(arg)).join(' ');\n\nconst SPACES_REGEXP = / +/g;\n\n// Handle `execaCommand()`\nexport const parseCommand = command => {\n\tconst tokens = [];\n\tfor (const token of command.trim().split(SPACES_REGEXP)) {\n\t\t// Allow spaces to be escaped by a backslash if not meant as a delimiter\n\t\tconst previousToken = tokens[tokens.length - 1];\n\t\tif (previousToken && previousToken.endsWith('\\\\')) {\n\t\t\t// Merge previous token with current one\n\t\t\ttokens[tokens.length - 1] = `${previousToken.slice(0, -1)} ${token}`;\n\t\t} else {\n\t\t\ttokens.push(token);\n\t\t}\n\t}\n\n\treturn tokens;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAAM,gBAAgB,CAAC,MAAM,OAAO,CAAC,MAAM;AAC1C,MAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACzB,WAAO,CAAC,IAAI;AAAA,EACb;AAEA,SAAO,CAAC,MAAM,GAAG,IAAI;AACtB;AAEA,MAAM,mBAAmB;AACzB,MAAM,uBAAuB;AAE7B,MAAM,YAAY,SAAO;AACxB,MAAI,OAAO,QAAQ,YAAY,iBAAiB,KAAK,GAAG,GAAG;AAC1D,WAAO;AAAA,EACR;AAEA,SAAO,IAAI,IAAI,QAAQ,sBAAsB,KAAK;AACnD;AAEO,MAAM,cAAc,CAAC,MAAM,SAAS,cAAc,MAAM,IAAI,EAAE,KAAK,GAAG;AAEtE,MAAM,oBAAoB,CAAC,MAAM,SAAS,cAAc,MAAM,IAAI,EAAE,IAAI,SAAO,UAAU,GAAG,CAAC,EAAE,KAAK,GAAG;AAE9G,MAAM,gBAAgB;AAGf,MAAM,eAAe,aAAW;AACtC,QAAM,SAAS,CAAC;AAChB,aAAW,SAAS,QAAQ,KAAK,EAAE,MAAM,aAAa,GAAG;AAExD,UAAM,gBAAgB,OAAO,OAAO,SAAS;AAC7C,QAAI,iBAAiB,cAAc,SAAS,IAAI,GAAG;AAElD,aAAO,OAAO,SAAS,KAAK,GAAG,cAAc,MAAM,GAAG,EAAE,KAAK;AAAA,IAC9D,OAAO;AACN,aAAO,KAAK,KAAK;AAAA,IAClB;AAAA,EACD;AAEA,SAAO;AACR;", "names": []}