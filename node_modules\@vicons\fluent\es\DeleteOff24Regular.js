import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3.94 5L2.22 3.28a.75.75 0 0 1 1.06-1.06l18.5 18.5a.75.75 0 0 1-1.06 1.06l-2.293-2.292l-.003.031a2.75 2.75 0 0 1-2.561 2.476l-.176.005H8.313a2.75 2.75 0 0 1-2.714-2.307l-.023-.174L4.295 6.5H3.5a.75.75 0 0 1-.743-.648L2.75 5.75a.75.75 0 0 1 .648-.743L3.5 5h.44zm13.115 13.116L14.5 15.56V17a.75.75 0 0 1-1.493.102L13 17v-2.94l-2-2V17a.75.75 0 0 1-1.493.102L9.5 17v-6.44L5.842 6.903l1.227 12.47a1.25 1.25 0 0 0 1.117 1.122l.127.006h7.374c.6 0 1.109-.425 1.225-1.002l.02-.126l.123-1.256zm-4.037-8.28l1.482 1.482V10l-.007-.102a.75.75 0 0 0-1.475-.062zM18.197 6.5l-.762 7.753l1.372 1.372l.897-9.125h.796l.102-.007A.75.75 0 0 0 20.5 5h-5.25l-.005-.184A3.25 3.25 0 0 0 8.75 5h-.568l1.5 1.5h8.515zM12 3.25c.966 0 1.75.784 1.75 1.75h-3.5l.006-.144A1.75 1.75 0 0 1 12 3.25z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'DeleteOff24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
