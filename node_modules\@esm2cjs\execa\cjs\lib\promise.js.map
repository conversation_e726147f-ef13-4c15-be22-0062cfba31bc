{"version": 3, "sources": ["../../esm/lib/promise.js"], "sourcesContent": ["const nativePromisePrototype = (async () => {})().constructor.prototype;\nconst descriptors = ['then', 'catch', 'finally'].map(property => [\n\tproperty,\n\tReflect.getOwnPropertyDescriptor(nativePromisePrototype, property),\n]);\n\n// The return value is a mixin of `childProcess` and `Promise`\nexport const mergePromise = (spawned, promise) => {\n\tfor (const [property, descriptor] of descriptors) {\n\t\t// Starting the main `promise` is deferred to avoid consuming streams\n\t\tconst value = typeof promise === 'function'\n\t\t\t? (...args) => Reflect.apply(descriptor.value, promise(), args)\n\t\t\t: descriptor.value.bind(promise);\n\n\t\tReflect.defineProperty(spawned, property, {...descriptor, value});\n\t}\n\n\treturn spawned;\n};\n\n// Use promises instead of `child_process` events\nexport const getSpawnedPromise = spawned => new Promise((resolve, reject) => {\n\tspawned.on('exit', (exitCode, signal) => {\n\t\tresolve({exitCode, signal});\n\t});\n\n\tspawned.on('error', error => {\n\t\treject(error);\n\t});\n\n\tif (spawned.stdin) {\n\t\tspawned.stdin.on('error', error => {\n\t\t\treject(error);\n\t\t});\n\t}\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAAM,0BAA0B,YAAY;AAAC,GAAG,EAAE,YAAY;AAC9D,MAAM,cAAc,CAAC,QAAQ,SAAS,SAAS,EAAE,IAAI,cAAY;AAAA,EAChE;AAAA,EACA,QAAQ,yBAAyB,wBAAwB,QAAQ;AAClE,CAAC;AAGM,MAAM,eAAe,CAAC,SAAS,YAAY;AACjD,aAAW,CAAC,UAAU,UAAU,KAAK,aAAa;AAEjD,UAAM,QAAQ,OAAO,YAAY,aAC9B,IAAI,SAAS,QAAQ,MAAM,WAAW,OAAO,QAAQ,GAAG,IAAI,IAC5D,WAAW,MAAM,KAAK,OAAO;AAEhC,YAAQ,eAAe,SAAS,UAAU,EAAC,GAAG,YAAY,MAAK,CAAC;AAAA,EACjE;AAEA,SAAO;AACR;AAGO,MAAM,oBAAoB,aAAW,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC5E,UAAQ,GAAG,QAAQ,CAAC,UAAU,WAAW;AACxC,YAAQ,EAAC,UAAU,OAAM,CAAC;AAAA,EAC3B,CAAC;AAED,UAAQ,GAAG,SAAS,WAAS;AAC5B,WAAO,KAAK;AAAA,EACb,CAAC;AAED,MAAI,QAAQ,OAAO;AAClB,YAAQ,MAAM,GAAG,SAAS,WAAS;AAClC,aAAO,KAAK;AAAA,IACb,CAAC;AAAA,EACF;AACD,CAAC;", "names": []}