import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3.94 5L2.22 3.28a.75.75 0 0 1 1.06-1.06l18.5 18.5a.75.75 0 0 1-1.06 1.06l-2.293-2.292l-.003.031A2.75 2.75 0 0 1 15.687 22H8.313a2.75 2.75 0 0 1-2.737-2.48L4.345 7H3.5a1 1 0 0 1 0-2h.44zM15 16.06l-1.5-1.5V17l.007.102a.75.75 0 0 0 1.486 0L15 17v-.94zm-4.5-4.5L9 10.06V17l.007.102a.75.75 0 0 0 1.486 0L10.5 17v-5.44zM15 10v1.818l3.807 3.807L19.655 7h.845l.117-.007A1 1 0 0 0 20.5 5h-5a3.5 3.5 0 1 0-7 0h-.318l5.318 5.318V10l.007-.102a.75.75 0 0 1 1.486 0L15 10zm-3-6.5A1.5 1.5 0 0 1 13.5 5h-3A1.5 1.5 0 0 1 12 3.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'DeleteOff24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
