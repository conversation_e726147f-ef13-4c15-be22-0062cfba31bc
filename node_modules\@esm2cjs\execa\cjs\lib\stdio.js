var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var stdio_exports = {};
__export(stdio_exports, {
  normalizeStdio: () => normalizeStdio,
  normalizeStdioNode: () => normalizeStdioNode
});
module.exports = __toCommonJS(stdio_exports);
const aliases = ["stdin", "stdout", "stderr"];
const hasAlias = (options) => aliases.some((alias) => options[alias] !== void 0);
const normalizeStdio = (options) => {
  if (!options) {
    return;
  }
  const { stdio } = options;
  if (stdio === void 0) {
    return aliases.map((alias) => options[alias]);
  }
  if (hasAlias(options)) {
    throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${aliases.map((alias) => `\`${alias}\``).join(", ")}`);
  }
  if (typeof stdio === "string") {
    return stdio;
  }
  if (!Array.isArray(stdio)) {
    throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof stdio}\``);
  }
  const length = Math.max(stdio.length, aliases.length);
  return Array.from({ length }, (value, index) => stdio[index]);
};
const normalizeStdioNode = (options) => {
  const stdio = normalizeStdio(options);
  if (stdio === "ipc") {
    return "ipc";
  }
  if (stdio === void 0 || typeof stdio === "string") {
    return [stdio, stdio, stdio, "ipc"];
  }
  if (stdio.includes("ipc")) {
    return stdio;
  }
  return [...stdio, "ipc"];
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  normalizeStdio,
  normalizeStdioNode
});
//# sourceMappingURL=stdio.js.map
