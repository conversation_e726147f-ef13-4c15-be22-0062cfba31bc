import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 12 12'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M7.812 3.078a.5.5 0 0 0 0 1c.14 0 .245.041.315.104c.066.058.146.174.166.41a.5.5 0 1 0 .997-.088c-.04-.445-.21-.814-.5-1.07a1.46 1.46 0 0 0-.978-.356zm-2.346-.979a2.321 2.321 0 0 0-1.954-.492a2.83 2.83 0 0 0-1.496.84c-.397.419-.799.981-.824 1.796c-.011.37.034.97.376 1.43c.311.436.47.662.552.882c.087.256.091.53.091.953v.007c.006.412.093.982.304 1.505c.207.512.575 1.075 1.204 1.307l.021.008l.023.006c.364.097.742.071 1.034-.2c.25-.233.342-.566.39-.824c.047-.244.07-.527.09-.792a7.98 7.98 0 0 1 .114-.954c.152-.702.335-.798.371-.81c.323-.085.515.02.646.32c.168.385.218.942.265 1.49c.047.537.16 1.064.4 1.418c.127.187.321.37.599.43c.28.062.542-.023.756-.159c.766-.483 1.176-1.467 1.281-2.687c.054-.565.111-.805.188-.992c.058-.139.122-.247.225-.422c.062-.105.138-.234.235-.407h.001c.154-.277.42-.8.45-1.422c.03-.656-.205-1.374-.962-1.981c-.698-.57-1.368-.793-1.985-.762c-.61.031-1.094.308-1.436.601l-.014.013l-.013.013c-.306.306-.397.302-.4.302c-.002 0-.037-.002-.122-.058a2.402 2.402 0 0 1-.369-.321l-.02-.02l-.02-.018zM2.75 3.126a1.83 1.83 0 0 1 .956-.538a1.32 1.32 0 0 1 1.112.272c.326.33.693.636 1.14.655c.469.02.834-.282 1.134-.58c.226-.19.502-.333.82-.35c.318-.015.753.09 1.304.54l.004.003c.501.402.606.809.59 1.155c-.019.38-.188.736-.324.98c-.061.11-.121.212-.179.31a5.14 5.14 0 0 0-.333.626c-.135.328-.203.688-.26 1.281v.005c-.095 1.098-.444 1.694-.819 1.93h-.001c-.088-.136-.181-.441-.223-.93l-.006-.064c-.042-.492-.103-1.196-.34-1.74c-.127-.293-.329-.595-.657-.78c-.355-.202-.786-.215-1.173-.104c-.558.16-.891.688-1.081 1.565l-.001.005a8.716 8.716 0 0 0-.13 1.054l-.001.017a7.072 7.072 0 0 1-.085.75c-.016.108-.033.22-.153.192c-.232-.095-.442-.343-.6-.735a3.17 3.17 0 0 1-.232-1.172c0-.385 0-.818-.147-1.248c-.153-.419-.427-.793-.693-1.147c-.135-.18-.189-.497-.18-.805c.015-.464.231-.801.553-1.141l.005-.006z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Dentist12Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
