{"name": "@esm2cjs/strip-final-newline", "version": "3.0.1-cjs.0", "description": "Strip the final newline character from a string/buffer. This is a fork of sindresorhus/strip-final-newline, but with CommonJS support.", "license": "MIT", "repository": "esm2cjs/strip-final-newline", "funding": "https://github.com/sponsors/AlCalzone", "author": {"name": "<PERSON>", "email": "d.gries<PERSON>@gmx.net"}, "exports": {".": {"import": "./esm/index.js", "require": "./cjs/index.js"}, "./package.json": "./package.json"}, "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava"}, "files": ["cjs/", "esm/"], "keywords": ["strip", "trim", "remove", "delete", "final", "last", "end", "file", "newline", "linebreak", "character", "string", "buffer"], "devDependencies": {"ava": "^3.15.0", "xo": "^0.39.1"}, "publishConfig": {"access": "public"}, "main": "cjs/index.js", "module": "esm/index.js", "xo": {"ignores": ["cjs"]}}