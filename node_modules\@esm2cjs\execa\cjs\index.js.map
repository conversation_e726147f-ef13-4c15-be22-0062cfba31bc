{"version": 3, "sources": ["../esm/index.js"], "sourcesContent": ["import {<PERSON><PERSON><PERSON>} from 'node:buffer';\nimport path from 'node:path';\nimport childProcess from 'node:child_process';\nimport process from 'node:process';\nimport crossSpawn from 'cross-spawn';\nimport stripFinalNewline from '@esm2cjs/strip-final-newline';\nimport {npmRunPathEnv} from '@esm2cjs/npm-run-path';\nimport onetime from '@esm2cjs/onetime';\nimport {makeError} from './lib/error.js';\nimport {normalizeStdio, normalizeStdioNode} from './lib/stdio.js';\nimport {spawnedKill, spawnedCancel, setupTimeout, validateTimeout, setExitHandler} from './lib/kill.js';\nimport {handleInput, getSpawnedResult, makeAllStream, validateInputSync} from './lib/stream.js';\nimport {mergePromise, getSpawnedPromise} from './lib/promise.js';\nimport {joinCommand, parseCommand, getEscapedCommand} from './lib/command.js';\n\nconst DEFAULT_MAX_BUFFER = 1000 * 1000 * 100;\n\nconst getEnv = ({env: envOption, extendEnv, preferLocal, localDir, execPath}) => {\n\tconst env = extendEnv ? {...process.env, ...envOption} : envOption;\n\n\tif (preferLocal) {\n\t\treturn npmRunPathEnv({env, cwd: localDir, execPath});\n\t}\n\n\treturn env;\n};\n\nconst handleArguments = (file, args, options = {}) => {\n\tconst parsed = crossSpawn._parse(file, args, options);\n\tfile = parsed.command;\n\targs = parsed.args;\n\toptions = parsed.options;\n\n\toptions = {\n\t\tmaxBuffer: DEFAULT_MAX_BUFFER,\n\t\tbuffer: true,\n\t\tstripFinalNewline: true,\n\t\textendEnv: true,\n\t\tpreferLocal: false,\n\t\tlocalDir: options.cwd || process.cwd(),\n\t\texecPath: process.execPath,\n\t\tencoding: 'utf8',\n\t\treject: true,\n\t\tcleanup: true,\n\t\tall: false,\n\t\twindowsHide: true,\n\t\t...options,\n\t};\n\n\toptions.env = getEnv(options);\n\n\toptions.stdio = normalizeStdio(options);\n\n\tif (process.platform === 'win32' && path.basename(file, '.exe') === 'cmd') {\n\t\t// #116\n\t\targs.unshift('/q');\n\t}\n\n\treturn {file, args, options, parsed};\n};\n\nconst handleOutput = (options, value, error) => {\n\tif (typeof value !== 'string' && !Buffer.isBuffer(value)) {\n\t\t// When `execaSync()` errors, we normalize it to '' to mimic `execa()`\n\t\treturn error === undefined ? undefined : '';\n\t}\n\n\tif (options.stripFinalNewline) {\n\t\treturn stripFinalNewline(value);\n\t}\n\n\treturn value;\n};\n\nexport function execa(file, args, options) {\n\tconst parsed = handleArguments(file, args, options);\n\tconst command = joinCommand(file, args);\n\tconst escapedCommand = getEscapedCommand(file, args);\n\n\tvalidateTimeout(parsed.options);\n\n\tlet spawned;\n\ttry {\n\t\tspawned = childProcess.spawn(parsed.file, parsed.args, parsed.options);\n\t} catch (error) {\n\t\t// Ensure the returned error is always both a promise and a child process\n\t\tconst dummySpawned = new childProcess.ChildProcess();\n\t\tconst errorPromise = Promise.reject(makeError({\n\t\t\terror,\n\t\t\tstdout: '',\n\t\t\tstderr: '',\n\t\t\tall: '',\n\t\t\tcommand,\n\t\t\tescapedCommand,\n\t\t\tparsed,\n\t\t\ttimedOut: false,\n\t\t\tisCanceled: false,\n\t\t\tkilled: false,\n\t\t}));\n\t\treturn mergePromise(dummySpawned, errorPromise);\n\t}\n\n\tconst spawnedPromise = getSpawnedPromise(spawned);\n\tconst timedPromise = setupTimeout(spawned, parsed.options, spawnedPromise);\n\tconst processDone = setExitHandler(spawned, parsed.options, timedPromise);\n\n\tconst context = {isCanceled: false};\n\n\tspawned.kill = spawnedKill.bind(null, spawned.kill.bind(spawned));\n\tspawned.cancel = spawnedCancel.bind(null, spawned, context);\n\n\tconst handlePromise = async () => {\n\t\tconst [{error, exitCode, signal, timedOut}, stdoutResult, stderrResult, allResult] = await getSpawnedResult(spawned, parsed.options, processDone);\n\t\tconst stdout = handleOutput(parsed.options, stdoutResult);\n\t\tconst stderr = handleOutput(parsed.options, stderrResult);\n\t\tconst all = handleOutput(parsed.options, allResult);\n\n\t\tif (error || exitCode !== 0 || signal !== null) {\n\t\t\tconst returnedError = makeError({\n\t\t\t\terror,\n\t\t\t\texitCode,\n\t\t\t\tsignal,\n\t\t\t\tstdout,\n\t\t\t\tstderr,\n\t\t\t\tall,\n\t\t\t\tcommand,\n\t\t\t\tescapedCommand,\n\t\t\t\tparsed,\n\t\t\t\ttimedOut,\n\t\t\t\tisCanceled: context.isCanceled || (parsed.options.signal ? parsed.options.signal.aborted : false),\n\t\t\t\tkilled: spawned.killed,\n\t\t\t});\n\n\t\t\tif (!parsed.options.reject) {\n\t\t\t\treturn returnedError;\n\t\t\t}\n\n\t\t\tthrow returnedError;\n\t\t}\n\n\t\treturn {\n\t\t\tcommand,\n\t\t\tescapedCommand,\n\t\t\texitCode: 0,\n\t\t\tstdout,\n\t\t\tstderr,\n\t\t\tall,\n\t\t\tfailed: false,\n\t\t\ttimedOut: false,\n\t\t\tisCanceled: false,\n\t\t\tkilled: false,\n\t\t};\n\t};\n\n\tconst handlePromiseOnce = onetime(handlePromise);\n\n\thandleInput(spawned, parsed.options.input);\n\n\tspawned.all = makeAllStream(spawned, parsed.options);\n\n\treturn mergePromise(spawned, handlePromiseOnce);\n}\n\nexport function execaSync(file, args, options) {\n\tconst parsed = handleArguments(file, args, options);\n\tconst command = joinCommand(file, args);\n\tconst escapedCommand = getEscapedCommand(file, args);\n\n\tvalidateInputSync(parsed.options);\n\n\tlet result;\n\ttry {\n\t\tresult = childProcess.spawnSync(parsed.file, parsed.args, parsed.options);\n\t} catch (error) {\n\t\tthrow makeError({\n\t\t\terror,\n\t\t\tstdout: '',\n\t\t\tstderr: '',\n\t\t\tall: '',\n\t\t\tcommand,\n\t\t\tescapedCommand,\n\t\t\tparsed,\n\t\t\ttimedOut: false,\n\t\t\tisCanceled: false,\n\t\t\tkilled: false,\n\t\t});\n\t}\n\n\tconst stdout = handleOutput(parsed.options, result.stdout, result.error);\n\tconst stderr = handleOutput(parsed.options, result.stderr, result.error);\n\n\tif (result.error || result.status !== 0 || result.signal !== null) {\n\t\tconst error = makeError({\n\t\t\tstdout,\n\t\t\tstderr,\n\t\t\terror: result.error,\n\t\t\tsignal: result.signal,\n\t\t\texitCode: result.status,\n\t\t\tcommand,\n\t\t\tescapedCommand,\n\t\t\tparsed,\n\t\t\ttimedOut: result.error && result.error.code === 'ETIMEDOUT',\n\t\t\tisCanceled: false,\n\t\t\tkilled: result.signal !== null,\n\t\t});\n\n\t\tif (!parsed.options.reject) {\n\t\t\treturn error;\n\t\t}\n\n\t\tthrow error;\n\t}\n\n\treturn {\n\t\tcommand,\n\t\tescapedCommand,\n\t\texitCode: 0,\n\t\tstdout,\n\t\tstderr,\n\t\tfailed: false,\n\t\ttimedOut: false,\n\t\tisCanceled: false,\n\t\tkilled: false,\n\t};\n}\n\nexport function execaCommand(command, options) {\n\tconst [file, ...args] = parseCommand(command);\n\treturn execa(file, args, options);\n}\n\nexport function execaCommandSync(command, options) {\n\tconst [file, ...args] = parseCommand(command);\n\treturn execaSync(file, args, options);\n}\n\nexport function execaNode(scriptPath, args, options = {}) {\n\tif (args && !Array.isArray(args) && typeof args === 'object') {\n\t\toptions = args;\n\t\targs = [];\n\t}\n\n\tconst stdio = normalizeStdioNode(options);\n\tconst defaultExecArgv = process.execArgv.filter(arg => !arg.startsWith('--inspect'));\n\n\tconst {\n\t\tnodePath = process.execPath,\n\t\tnodeOptions = defaultExecArgv,\n\t} = options;\n\n\treturn execa(\n\t\tnodePath,\n\t\t[\n\t\t\t...nodeOptions,\n\t\t\tscriptPath,\n\t\t\t...(Array.isArray(args) ? args : []),\n\t\t],\n\t\t{\n\t\t\t...options,\n\t\t\tstdin: undefined,\n\t\t\tstdout: undefined,\n\t\t\tstderr: undefined,\n\t\t\tstdio,\n\t\t\tshell: false,\n\t\t},\n\t);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAAqB;AACrB,uBAAiB;AACjB,gCAAyB;AACzB,0BAAoB;AACpB,yBAAuB;AACvB,iCAA8B;AAC9B,0BAA4B;AAC5B,qBAAoB;AACpB,mBAAwB;AACxB,mBAAiD;AACjD,kBAAwF;AACxF,oBAA8E;AAC9E,qBAA8C;AAC9C,qBAA2D;AAE3D,MAAM,qBAAqB,MAAO,MAAO;AAEzC,MAAM,SAAS,CAAC,EAAC,KAAK,WAAW,WAAW,aAAa,UAAU,SAAQ,MAAM;AAChF,QAAM,MAAM,YAAY,EAAC,GAAG,oBAAAA,QAAQ,KAAK,GAAG,UAAS,IAAI;AAEzD,MAAI,aAAa;AAChB,eAAO,mCAAc,EAAC,KAAK,KAAK,UAAU,SAAQ,CAAC;AAAA,EACpD;AAEA,SAAO;AACR;AAEA,MAAM,kBAAkB,CAAC,MAAM,MAAM,UAAU,CAAC,MAAM;AACrD,QAAM,SAAS,mBAAAC,QAAW,OAAO,MAAM,MAAM,OAAO;AACpD,SAAO,OAAO;AACd,SAAO,OAAO;AACd,YAAU,OAAO;AAEjB,YAAU;AAAA,IACT,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,mBAAmB;AAAA,IACnB,WAAW;AAAA,IACX,aAAa;AAAA,IACb,UAAU,QAAQ,OAAO,oBAAAD,QAAQ,IAAI;AAAA,IACrC,UAAU,oBAAAA,QAAQ;AAAA,IAClB,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,KAAK;AAAA,IACL,aAAa;AAAA,IACb,GAAG;AAAA,EACJ;AAEA,UAAQ,MAAM,OAAO,OAAO;AAE5B,UAAQ,YAAQ,6BAAe,OAAO;AAEtC,MAAI,oBAAAA,QAAQ,aAAa,WAAW,iBAAAE,QAAK,SAAS,MAAM,MAAM,MAAM,OAAO;AAE1E,SAAK,QAAQ,IAAI;AAAA,EAClB;AAEA,SAAO,EAAC,MAAM,MAAM,SAAS,OAAM;AACpC;AAEA,MAAM,eAAe,CAAC,SAAS,OAAO,UAAU;AAC/C,MAAI,OAAO,UAAU,YAAY,CAAC,0BAAO,SAAS,KAAK,GAAG;AAEzD,WAAO,UAAU,SAAY,SAAY;AAAA,EAC1C;AAEA,MAAI,QAAQ,mBAAmB;AAC9B,eAAO,2BAAAC,SAAkB,KAAK;AAAA,EAC/B;AAEA,SAAO;AACR;AAEO,SAAS,MAAM,MAAM,MAAM,SAAS;AAC1C,QAAM,SAAS,gBAAgB,MAAM,MAAM,OAAO;AAClD,QAAM,cAAU,4BAAY,MAAM,IAAI;AACtC,QAAM,qBAAiB,kCAAkB,MAAM,IAAI;AAEnD,mCAAgB,OAAO,OAAO;AAE9B,MAAI;AACJ,MAAI;AACH,cAAU,0BAAAC,QAAa,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,OAAO;AAAA,EACtE,SAAS,OAAP;AAED,UAAM,eAAe,IAAI,0BAAAA,QAAa,aAAa;AACnD,UAAM,eAAe,QAAQ,WAAO,wBAAU;AAAA,MAC7C;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,QAAQ;AAAA,IACT,CAAC,CAAC;AACF,eAAO,6BAAa,cAAc,YAAY;AAAA,EAC/C;AAEA,QAAM,qBAAiB,kCAAkB,OAAO;AAChD,QAAM,mBAAe,0BAAa,SAAS,OAAO,SAAS,cAAc;AACzE,QAAM,kBAAc,4BAAe,SAAS,OAAO,SAAS,YAAY;AAExE,QAAM,UAAU,EAAC,YAAY,MAAK;AAElC,UAAQ,OAAO,wBAAY,KAAK,MAAM,QAAQ,KAAK,KAAK,OAAO,CAAC;AAChE,UAAQ,SAAS,0BAAc,KAAK,MAAM,SAAS,OAAO;AAE1D,QAAM,gBAAgB,YAAY;AACjC,UAAM,CAAC,EAAC,OAAO,UAAU,QAAQ,SAAQ,GAAG,cAAc,cAAc,SAAS,IAAI,UAAM,gCAAiB,SAAS,OAAO,SAAS,WAAW;AAChJ,UAAM,SAAS,aAAa,OAAO,SAAS,YAAY;AACxD,UAAM,SAAS,aAAa,OAAO,SAAS,YAAY;AACxD,UAAM,MAAM,aAAa,OAAO,SAAS,SAAS;AAElD,QAAI,SAAS,aAAa,KAAK,WAAW,MAAM;AAC/C,YAAM,oBAAgB,wBAAU;AAAA,QAC/B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,YAAY,QAAQ,eAAe,OAAO,QAAQ,SAAS,OAAO,QAAQ,OAAO,UAAU;AAAA,QAC3F,QAAQ,QAAQ;AAAA,MACjB,CAAC;AAED,UAAI,CAAC,OAAO,QAAQ,QAAQ;AAC3B,eAAO;AAAA,MACR;AAEA,YAAM;AAAA,IACP;AAEA,WAAO;AAAA,MACN;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,QAAQ;AAAA,IACT;AAAA,EACD;AAEA,QAAM,wBAAoB,eAAAC,SAAQ,aAAa;AAE/C,iCAAY,SAAS,OAAO,QAAQ,KAAK;AAEzC,UAAQ,UAAM,6BAAc,SAAS,OAAO,OAAO;AAEnD,aAAO,6BAAa,SAAS,iBAAiB;AAC/C;AAEO,SAAS,UAAU,MAAM,MAAM,SAAS;AAC9C,QAAM,SAAS,gBAAgB,MAAM,MAAM,OAAO;AAClD,QAAM,cAAU,4BAAY,MAAM,IAAI;AACtC,QAAM,qBAAiB,kCAAkB,MAAM,IAAI;AAEnD,uCAAkB,OAAO,OAAO;AAEhC,MAAI;AACJ,MAAI;AACH,aAAS,0BAAAD,QAAa,UAAU,OAAO,MAAM,OAAO,MAAM,OAAO,OAAO;AAAA,EACzE,SAAS,OAAP;AACD,cAAM,wBAAU;AAAA,MACf;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,QAAQ;AAAA,IACT,CAAC;AAAA,EACF;AAEA,QAAM,SAAS,aAAa,OAAO,SAAS,OAAO,QAAQ,OAAO,KAAK;AACvE,QAAM,SAAS,aAAa,OAAO,SAAS,OAAO,QAAQ,OAAO,KAAK;AAEvE,MAAI,OAAO,SAAS,OAAO,WAAW,KAAK,OAAO,WAAW,MAAM;AAClE,UAAM,YAAQ,wBAAU;AAAA,MACvB;AAAA,MACA;AAAA,MACA,OAAO,OAAO;AAAA,MACd,QAAQ,OAAO;AAAA,MACf,UAAU,OAAO;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU,OAAO,SAAS,OAAO,MAAM,SAAS;AAAA,MAChD,YAAY;AAAA,MACZ,QAAQ,OAAO,WAAW;AAAA,IAC3B,CAAC;AAED,QAAI,CAAC,OAAO,QAAQ,QAAQ;AAC3B,aAAO;AAAA,IACR;AAEA,UAAM;AAAA,EACP;AAEA,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,QAAQ;AAAA,EACT;AACD;AAEO,SAAS,aAAa,SAAS,SAAS;AAC9C,QAAM,CAAC,SAAS,IAAI,QAAI,6BAAa,OAAO;AAC5C,SAAO,MAAM,MAAM,MAAM,OAAO;AACjC;AAEO,SAAS,iBAAiB,SAAS,SAAS;AAClD,QAAM,CAAC,SAAS,IAAI,QAAI,6BAAa,OAAO;AAC5C,SAAO,UAAU,MAAM,MAAM,OAAO;AACrC;AAEO,SAAS,UAAU,YAAY,MAAM,UAAU,CAAC,GAAG;AACzD,MAAI,QAAQ,CAAC,MAAM,QAAQ,IAAI,KAAK,OAAO,SAAS,UAAU;AAC7D,cAAU;AACV,WAAO,CAAC;AAAA,EACT;AAEA,QAAM,YAAQ,iCAAmB,OAAO;AACxC,QAAM,kBAAkB,oBAAAJ,QAAQ,SAAS,OAAO,SAAO,CAAC,IAAI,WAAW,WAAW,CAAC;AAEnF,QAAM;AAAA,IACL,WAAW,oBAAAA,QAAQ;AAAA,IACnB,cAAc;AAAA,EACf,IAAI;AAEJ,SAAO;AAAA,IACN;AAAA,IACA;AAAA,MACC,GAAG;AAAA,MACH;AAAA,MACA,GAAI,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC;AAAA,IACnC;AAAA,IACA;AAAA,MACC,GAAG;AAAA,MACH,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR;AAAA,MACA,OAAO;AAAA,IACR;AAAA,EACD;AACD;", "names": ["process", "crossSpawn", "path", "stripFinalNewline", "childProcess", "onetime"]}