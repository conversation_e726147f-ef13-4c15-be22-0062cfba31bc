{"version": 3, "sources": ["../esm/index.js"], "sourcesContent": ["import process from 'node:process';\nimport path from 'node:path';\nimport url from 'node:url';\nimport pathKey from '@esm2cjs/path-key';\n\nexport function npmRunPath(options = {}) {\n\tconst {\n\t\tcwd = process.cwd(),\n\t\tpath: path_ = process.env[pathKey()],\n\t\texecPath = process.execPath,\n\t} = options;\n\n\tlet previous;\n\tconst cwdString = cwd instanceof URL ? url.fileURLToPath(cwd) : cwd;\n\tlet cwdPath = path.resolve(cwdString);\n\tconst result = [];\n\n\twhile (previous !== cwdPath) {\n\t\tresult.push(path.join(cwdPath, 'node_modules/.bin'));\n\t\tprevious = cwdPath;\n\t\tcwdPath = path.resolve(cwdPath, '..');\n\t}\n\n\t// Ensure the running `node` binary is used.\n\tresult.push(path.resolve(cwdString, execPath, '..'));\n\n\treturn [...result, path_].join(path.delimiter);\n}\n\nexport function npmRunPathEnv({env = process.env, ...options} = {}) {\n\tenv = {...env};\n\n\tconst path = pathKey({env});\n\toptions.path = env[path];\n\tenv[path] = npmRunPath(options);\n\n\treturn env;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAAoB;AACpB,uBAAiB;AACjB,sBAAgB;AAChB,sBAAoB;AAEb,SAAS,WAAW,UAAU,CAAC,GAAG;AACxC,QAAM;AAAA,IACL,MAAM,oBAAAA,QAAQ,IAAI;AAAA,IAClB,MAAM,QAAQ,oBAAAA,QAAQ,QAAI,gBAAAC,SAAQ;AAAA,IAClC,WAAW,oBAAAD,QAAQ;AAAA,EACpB,IAAI;AAEJ,MAAI;AACJ,QAAM,YAAY,eAAe,MAAM,gBAAAE,QAAI,cAAc,GAAG,IAAI;AAChE,MAAI,UAAU,iBAAAC,QAAK,QAAQ,SAAS;AACpC,QAAM,SAAS,CAAC;AAEhB,SAAO,aAAa,SAAS;AAC5B,WAAO,KAAK,iBAAAA,QAAK,KAAK,SAAS,mBAAmB,CAAC;AACnD,eAAW;AACX,cAAU,iBAAAA,QAAK,QAAQ,SAAS,IAAI;AAAA,EACrC;AAGA,SAAO,KAAK,iBAAAA,QAAK,QAAQ,WAAW,UAAU,IAAI,CAAC;AAEnD,SAAO,CAAC,GAAG,QAAQ,KAAK,EAAE,KAAK,iBAAAA,QAAK,SAAS;AAC9C;AAEO,SAAS,cAAc,EAAC,MAAM,oBAAAH,QAAQ,QAAQ,QAAO,IAAI,CAAC,GAAG;AACnE,QAAM,EAAC,GAAG,IAAG;AAEb,QAAMG,YAAO,gBAAAF,SAAQ,EAAC,IAAG,CAAC;AAC1B,UAAQ,OAAO,IAAIE;AACnB,MAAIA,SAAQ,WAAW,OAAO;AAE9B,SAAO;AACR;", "names": ["process", "path<PERSON><PERSON>", "url", "path"]}