{"version": 3, "sources": ["../esm/index.js"], "sourcesContent": ["export default function stripFinalNewline(input) {\n\tconst LF = typeof input === 'string' ? '\\n' : '\\n'.charCodeAt();\n\tconst CR = typeof input === 'string' ? '\\r' : '\\r'.charCodeAt();\n\n\tif (input[input.length - 1] === LF) {\n\t\tinput = input.slice(0, -1);\n\t}\n\n\tif (input[input.length - 1] === CR) {\n\t\tinput = input.slice(0, -1);\n\t}\n\n\treturn input;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAe,SAAR,kBAAmC,OAAO;AAChD,QAAM,KAAK,OAAO,UAAU,WAAW,OAAO,KAAK,WAAW;AAC9D,QAAM,KAAK,OAAO,UAAU,WAAW,OAAO,KAAK,WAAW;AAE9D,MAAI,MAAM,MAAM,SAAS,OAAO,IAAI;AACnC,YAAQ,MAAM,MAAM,GAAG,EAAE;AAAA,EAC1B;AAEA,MAAI,MAAM,MAAM,SAAS,OAAO,IAAI;AACnC,YAAQ,MAAM,MAAM,GAAG,EAAE;AAAA,EAC1B;AAEA,SAAO;AACR;", "names": []}