import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2.854 2.146a.5.5 0 1 0-.708.708L3.414 4.12C3.152 4.517 3 4.991 3 5.5v9A2.5 2.5 0 0 0 5.5 17h9c.51 0 .983-.152 1.379-.414l1.267 1.268a.5.5 0 0 0 .708-.708l-15-15zm11.905 13.32a1 1 0 0 1-.259.034h-9a1 1 0 0 1-1-1V7h1.793l8.466 8.466zM15.5 7v6.379l1.475 1.475c.016-.116.025-.234.025-.354v-9A2.5 2.5 0 0 0 14.5 3h-9c-.12 0-.238.008-.354.025L9.121 7H15.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowHeaderHorizontalOff20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
