import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M5.498 3.001a2.5 2.5 0 0 0-2.5 2.5v9a2.5 2.5 0 0 0 2.5 2.5h9a2.5 2.5 0 0 0 2.5-2.5v-7.5H17v-1h-.002v-.5a2.5 2.5 0 0 0-2.5-2.5h-9zm10.5 2.5v.5h-1.29l1.144-1.144c.093.195.146.413.146.644zm-.851-1.353l-1.853 1.853h-2.829l2-2h2.033c.232 0 .452.053.649.147zm-4.096-.147l-2 2H6.223l2-2h2.828zm-4.242 0l-2 2h-.811v-.5a1.5 1.5 0 0 1 1.5-1.5h1.31zM3.998 8.226l1.225-1.225H8.05l-4.053 4.054V8.226zm5.467-1.225h2.829l-8.15 8.149a1.494 1.494 0 0 1-.146-.649v-2.032L9.465 7zm4.243 0h2.29v.539L7.537 16h-2.04c-.23 0-.449-.052-.644-.145L13.708 7zm2.29 1.953v2.829L11.779 16H8.951l7.047-7.047zm0 4.243V14.5a1.5 1.5 0 0 1-1.5 1.5h-1.305l2.805-2.804z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowInprivate20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
