import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M7.5 3.5c-.56 0-1.083.153-1.53.42a3.398 3.398 0 0 0-.48-1.448A4.5 4.5 0 0 1 11.973 6h1.278c.966 0 1.75.784 1.75 1.75v4.5A1.75 1.75 0 0 1 13.25 14h-4.5A1.75 1.75 0 0 1 7 12.25v-1.277a4.474 4.474 0 0 1-1.016-.235c.01-.236.016-.482.016-.738v-.08c0-.24 0-.523-.01-.827A2.98 2.98 0 0 0 7 9.46V7.75C7 6.784 7.784 6 8.75 6h1.709A3 3 0 0 0 7.5 3.5zm4.389 4A4.507 4.507 0 0 1 8.5 10.889v1.361c0 .138.112.25.25.25h4.5a.25.25 0 0 0 .25-.25v-4.5a.25.25 0 0 0-.25-.25h-1.361zm-1.56 0H8.75a.25.25 0 0 0-.25.25v1.58a3.008 3.008 0 0 0 1.83-1.83zM3.581 1.125a.5.5 0 0 1 .119.598a.33.33 0 0 0-.013.09c0 .063.016.183.167.333c.073.073.129.125.19.182c.05.046.103.094.17.16c.13.124.267.27.39.453c.255.383.396.862.396 1.559c0 .832-.3 1.543-.772 1.992c.292.296.462.683.567 1.07C5 8.314 5 9.244 5 9.963V10c0 2.058-.385 3.28-.821 4.007a2.674 2.674 0 0 1-.638.747a1.645 1.645 0 0 1-.33.2S3.084 15 3 15a.8.8 0 0 1-.211-.046a1.645 1.645 0 0 1-.33-.2a2.674 2.674 0 0 1-.638-.747C1.385 13.281 1 12.058 1 10v-.036c0-.72 0-1.649.205-2.403c.105-.386.275-.773.567-1.07C1.3 6.044 1 5.332 1 4.5c0-.326.087-.715.207-1.074c.12-.36.288-.732.482-1.032c.231-.39.556-.717.808-.937a5.487 5.487 0 0 1 .432-.343l.03-.02l.009-.007l.003-.002l.002-.001a.5.5 0 0 1 .608.041z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'DesignIdeas16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
