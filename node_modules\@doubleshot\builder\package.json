{"name": "@doubleshot/builder", "version": "0.0.11", "author": "ArcherGu (https://github.com/ArcherGu/)", "license": "MIT", "homepage": "https://github.com/archergu/doubleshot/tree/main/packages/builder#readme", "repository": {"type": "git", "url": "git+https://github.com/archergu/doubleshot.git", "directory": "packages/builder"}, "bugs": {"url": "https://github.com/archergu/doubleshot/issues"}, "keywords": ["bundle", "builder", "electron", "<PERSON><PERSON><PERSON>", "esbuild"], "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"doubleshot-build": "dist/cli.js", "dsb": "dist/cli.js"}, "files": ["dist"], "engines": {"node": ">=14.0.0"}, "peerDependencies": {"electron": ">=16.0.0", "electron-builder": ">=20.0.0"}, "peerDependenciesMeta": {"electron": {"optional": true}, "electron-builder": {"optional": true}}, "dependencies": {"@swc/core": "^1.3.62", "bundle-require": "^4.0.1", "cac": "^6.7.14", "check-package-exists": "^1.1.3", "colorette": "^2.0.20", "esbuild": "^0.18.0", "joycon": "^3.1.1", "resolve-from": "^5.0.0", "tsup": "^7.0.0", "wait-on": "^7.0.1"}, "devDependencies": {"@types/wait-on": "5.3.1", "electron": "25.1.1", "electron-builder": "24.4.0"}, "scripts": {"build": "tsup", "test": "tsup && vitest run"}}