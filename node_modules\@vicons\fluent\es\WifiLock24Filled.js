import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6.5 12a5.5 5.5 0 1 1 0 11a5.5 5.5 0 0 1 0-11zm0 1.998a1.75 1.75 0 0 0-1.744 1.606l-.006.144v.781c-.395.102-.696.44-.743.854L4 17.498v2.5a1 1 0 0 0 .883.993l.117.007h3a1 1 0 0 0 .993-.884L9 19.998v-2.5a1 1 0 0 0-.644-.935l-.105-.034l-.001-.781a1.75 1.75 0 0 0-1.75-1.75zm9.256-.297c.46.46.838 1.024 1.101 1.624a1 1 0 0 1-1.832.803a3.222 3.222 0 0 0-2.943-1.96a6.519 6.519 0 0 0-1.556-1.772a5.23 5.23 0 0 1 5.23 1.305zM6.5 14.998a.75.75 0 0 1 .743.648l.007.102v.75h-1.5v-.75a.75.75 0 0 1 .75-.75zm11.305-4.289a8.35 8.35 0 0 1 1.503 2.085a1 1 0 0 1-1.78.91a6.357 6.357 0 0 0-1.137-1.58a6.13 6.13 0 0 0-7.742-.761a6.493 6.493 0 0 0-2.631-.346a8.128 8.128 0 0 1 11.787-.308zM20.44 8.3c.508.509.987 1.088 1.404 1.692a1 1 0 0 1-1.646 1.136c-.35-.507-.752-.993-1.172-1.413c-3.873-3.873-10.15-3.873-14.023 0c-.4.399-.797.885-1.16 1.41a1 1 0 1 1-1.644-1.14c.428-.618.9-1.195 1.39-1.684c4.653-4.654 12.198-4.654 16.851 0z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WifiLock24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
