import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M5.498 3.001a2.5 2.5 0 0 0-2.5 2.5v9a2.5 2.5 0 0 0 2.5 2.5h9a2.5 2.5 0 0 0 2.5-2.5V8H17V6.5h-.002v-.999a2.5 2.5 0 0 0-2.5-2.5h-9zM15.5 6v.5h-1.29l1.144-1.145c.094.195.146.414.146.645zm-.852-1.353L12.795 6.5H9.966l2-2H14c.232 0 .452.053.648.147zM10.552 4.5l-2 2H5.724l2-2h2.828zm-4.242 0L4.5 6.31V6A1.5 1.5 0 0 1 6 4.5h.31zM7.052 8L4.5 10.552V8h2.552zM4.5 11.966L8.466 8h2.829l-6.648 6.648A1.494 1.494 0 0 1 4.5 14v-2.034zM12.71 8h2.79v.037L8.037 15.5H6c-.231 0-.45-.052-.645-.146L12.709 8zm2.79 1.452v2.828l-3.22 3.22H9.452L15.5 9.452zm0 4.242V14a1.5 1.5 0 0 1-1.5 1.5h-.306l1.806-1.806z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowInprivate20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
