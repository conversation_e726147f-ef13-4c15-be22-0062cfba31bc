import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4.5 2.001a2.5 2.5 0 0 0-2.5 2.5v6a2.5 2.5 0 0 0 2.5 2.5h4.281c.033-.216.105-.43.221-.63l.214-.37H4.5a1.5 1.5 0 0 1-1.5-1.5V5h9v1.194a2.926 2.926 0 0 1 1-.204V4.5a2.5 2.5 0 0 0-2.5-2.5h-6zM3.084 4a1.5 1.5 0 0 1 1.415-1h6a1.5 1.5 0 0 1 1.414 1H3.084zm7.332 3.71L8.854 6.146a.5.5 0 0 0-.707.707l1.646 1.647l-1.646 1.646a.5.5 0 1 0 .707.707l1.368-1.368a2.898 2.898 0 0 1 .194-1.777zM6.853 6.146a.5.5 0 0 1 0 .707L5.207 8.501l1.646 1.646a.5.5 0 1 1-.707.707l-2-2a.5.5 0 0 1 0-.707l2-2a.5.5 0 0 1 .707 0zM12.52 8.58l.9-1.56a1.91 1.91 0 0 0-1.902 2.992l-1.651 2.86a.796.796 0 0 0 1.378.795l1.655-2.865a1.91 1.91 0 0 0 1.623-3.144l-.9 1.559a.637.637 0 1 1-1.103-.637z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowDevTools16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
