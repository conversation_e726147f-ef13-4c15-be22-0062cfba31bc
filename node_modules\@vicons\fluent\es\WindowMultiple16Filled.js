import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4.25 2A2.25 2.25 0 0 0 2 4.25v5A2.75 2.75 0 0 0 4.75 12h4.5A2.75 2.75 0 0 0 12 9.25v-5A2.25 2.25 0 0 0 9.75 2h-5.5zM3.5 9.25V5h7v4.25c0 .69-.56 1.25-1.25 1.25h-4.5c-.69 0-1.25-.56-1.25-1.25zm3.75 5.25A2.75 2.75 0 0 1 4.8 13h5.45A2.75 2.75 0 0 0 13 10.25V7.139V4.758a2.64 2.64 0 0 1 1.5 2.38v3.112a4.25 4.25 0 0 1-4.25 4.25h-3z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowMultiple16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
