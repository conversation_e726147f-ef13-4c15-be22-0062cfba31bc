{"version": 3, "sources": ["../esm/index.js"], "sourcesContent": ["import mimicFunction from '@esm2cjs/mimic-fn';\n\nconst calledFunctions = new WeakMap();\n\nconst onetime = (function_, options = {}) => {\n\tif (typeof function_ !== 'function') {\n\t\tthrow new TypeError('Expected a function');\n\t}\n\n\tlet returnValue;\n\tlet callCount = 0;\n\tconst functionName = function_.displayName || function_.name || '<anonymous>';\n\n\tconst onetime = function (...arguments_) {\n\t\tcalledFunctions.set(onetime, ++callCount);\n\n\t\tif (callCount === 1) {\n\t\t\treturnValue = function_.apply(this, arguments_);\n\t\t\tfunction_ = null;\n\t\t} else if (options.throw === true) {\n\t\t\tthrow new Error(`Function \\`${functionName}\\` can only be called once`);\n\t\t}\n\n\t\treturn returnValue;\n\t};\n\n\tmimicFunction(onetime, function_);\n\tcalledFunctions.set(onetime, callCount);\n\n\treturn onetime;\n};\n\nonetime.callCount = function_ => {\n\tif (!calledFunctions.has(function_)) {\n\t\tthrow new Error(`The given function \\`${function_.name}\\` is not wrapped by the \\`onetime\\` package`);\n\t}\n\n\treturn calledFunctions.get(function_);\n};\n\nexport default onetime;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAA0B;AAE1B,MAAM,kBAAkB,oBAAI,QAAQ;AAEpC,MAAM,UAAU,CAAC,WAAW,UAAU,CAAC,MAAM;AAC5C,MAAI,OAAO,cAAc,YAAY;AACpC,UAAM,IAAI,UAAU,qBAAqB;AAAA,EAC1C;AAEA,MAAI;AACJ,MAAI,YAAY;AAChB,QAAM,eAAe,UAAU,eAAe,UAAU,QAAQ;AAEhE,QAAMA,WAAU,YAAa,YAAY;AACxC,oBAAgB,IAAIA,UAAS,EAAE,SAAS;AAExC,QAAI,cAAc,GAAG;AACpB,oBAAc,UAAU,MAAM,MAAM,UAAU;AAC9C,kBAAY;AAAA,IACb,WAAW,QAAQ,UAAU,MAAM;AAClC,YAAM,IAAI,MAAM,cAAc,wCAAwC;AAAA,IACvE;AAEA,WAAO;AAAA,EACR;AAEA,sBAAAC,SAAcD,UAAS,SAAS;AAChC,kBAAgB,IAAIA,UAAS,SAAS;AAEtC,SAAOA;AACR;AAEA,QAAQ,YAAY,eAAa;AAChC,MAAI,CAAC,gBAAgB,IAAI,SAAS,GAAG;AACpC,UAAM,IAAI,MAAM,wBAAwB,UAAU,kDAAkD;AAAA,EACrG;AAEA,SAAO,gBAAgB,IAAI,SAAS;AACrC;AAEA,IAAO,cAAQ;", "names": ["onetime", "mimicFunction"]}