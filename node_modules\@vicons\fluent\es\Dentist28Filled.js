import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M12.356 4.644c-1.439-1.157-3.034-1.402-4.464-1.123c-1.406.275-2.65 1.053-3.477 1.949l-.002.002c-.927 1.011-1.836 2.275-1.906 4.145c-.043.931.084 2.278.812 3.282l.002.002c.781 1.07 1.235 1.706 1.46 2.324c.25.685.255 1.369.263 2.334l.002.205c.006.994.216 2.357.72 3.596c.5 1.224 1.347 2.47 2.718 2.981l.035.013l.036.01c.807.212 1.552.134 2.113-.39c.495-.465.702-1.157.818-1.782c.107-.575.16-1.243.212-1.9l.02-.25c.062-.758.133-1.515.282-2.229c.42-1.972 1.006-2.327 1.286-2.412c.677-.174 1.102-.085 1.39.079c.306.175.575.502.798 1.013c.46 1.051.592 2.536.705 3.879c.112 1.336.394 2.543.928 3.314c.276.4.667.748 1.194.861c.532.115 1.05-.042 1.51-.337c1.7-1.086 2.684-3.336 2.952-6.325c.128-1.423.275-2.08.496-2.614c.113-.273.253-.532.447-.866l.186-.319c.156-.263.338-.572.538-.93c.38-.677.994-1.9 1.064-3.33c.073-1.484-.445-3.125-2.195-4.55c-1.68-1.365-3.243-1.86-4.637-1.786c-1.381.073-2.485.702-3.274 1.38l-.021.017l-.02.02c-.766.761-1.126.902-1.363.892c-.268-.011-.697-.22-1.564-1.098l-.03-.03l-.034-.027zm6.17 2.565c.752 0 1.514.225 2.117.762c.609.541.985 1.336 1.08 2.338a.75.75 0 1 1-1.494.14c-.065-.698-.309-1.114-.583-1.357c-.279-.248-.662-.383-1.12-.383a.75.75 0 0 1 0-1.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Dentist28Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
