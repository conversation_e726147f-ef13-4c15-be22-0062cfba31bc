import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3 6a3 3 0 0 1 3-3h8a3 3 0 0 1 3 3v2.624a1.383 1.383 0 0 0-.993-.594H16V7H4v7a2 2 0 0 0 2 2h2.492a2.493 2.493 0 0 0-.443 1H6a3 3 0 0 1-3-3V6zm3-2a2 2 0 0 0-2 2h12a2 2 0 0 0-2-2H6zm10.242 5.361a.387.387 0 0 0-.35-.337a3.518 3.518 0 0 0-3.723 4.676l-2.727 2.726a1.508 1.508 0 0 0 2.132 2.132l2.726-2.726a3.518 3.518 0 0 0 4.676-3.724c-.042-.366-.48-.471-.742-.21l-.975.975a1.508 1.508 0 0 1-2.132-2.132l.975-.975a.489.489 0 0 0 .14-.405z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowWrench20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
