import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2.854 2.146l15 15a.5.5 0 0 1-.708.708l-1.267-1.268c-.396.262-.87.414-1.379.414h-9A2.5 2.5 0 0 1 3 14.5v-9c0-.51.152-.983.414-1.379L2.146 2.854a.5.5 0 1 1 .708-.708zm11.853 13.268L11 11.707v.793a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-4a.5.5 0 0 1 .5-.5h.793l-1-1H4.5v7.25c0 .69.56 1.25 1.25 1.25h8.5c.161 0 .315-.03.457-.086zM8.293 9H7v3h3v-1.293L8.293 9zm7.207 4.379V7H9.121L5.146 3.025C5.262 3.008 5.38 3 5.5 3h9A2.5 2.5 0 0 1 17 5.5v9c0 .12-.009.238-.025.354L15.5 13.379z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowAdOff20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
