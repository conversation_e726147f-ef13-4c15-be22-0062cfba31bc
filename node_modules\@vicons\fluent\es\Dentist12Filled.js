import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 12 12'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M5.466 2.1a2.321 2.321 0 0 0-1.954-.493a2.83 2.83 0 0 0-1.496.84c-.397.419-.799.981-.824 1.796c-.011.37.034.97.376 1.43c.311.436.47.662.552.882c.087.256.091.53.091.953v.007c.006.412.093.982.304 1.505c.207.512.575 1.075 1.204 1.307l.021.008l.023.006c.364.097.742.071 1.034-.2c.25-.233.342-.566.39-.824c.047-.244.07-.527.09-.792l.007-.09c.024-.303.051-.593.107-.864c.152-.702.335-.798.371-.81c.236-.062.352-.025.412.01c.071.04.155.128.234.31c.168.385.218.942.265 1.49c.047.537.16 1.064.4 1.418c.127.187.321.37.599.43c.28.062.542-.023.756-.159c.766-.483 1.176-1.467 1.281-2.687c.054-.565.111-.805.188-.992c.058-.139.122-.247.225-.422c.062-.105.138-.234.235-.407h.001c.154-.277.42-.8.45-1.422c.03-.656-.205-1.374-.962-1.981c-.698-.57-1.368-.793-1.985-.762c-.61.031-1.094.308-1.436.601l-.014.013l-.013.013c-.306.306-.397.302-.4.302c-.002 0-.037-.002-.122-.058a2.402 2.402 0 0 1-.369-.321l-.02-.02l-.02-.018zm2.345.978c.34 0 .695.103.98.356c.288.256.46.625.498 1.07a.5.5 0 1 1-.996.087c-.02-.235-.1-.35-.166-.41a.464.464 0 0 0-.316-.103a.5.5 0 1 1 0-1z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Dentist12Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
