import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3.28 2.22a.75.75 0 1 0-1.06 1.06l3.364 3.365a11.99 11.99 0 0 0-2.033 1.634c-.49.489-.964 1.069-1.394 1.69a.75.75 0 1 0 1.233.854c.381-.55.8-1.062 1.222-1.484a10.467 10.467 0 0 1 2.062-1.604l1.592 1.592a8.059 8.059 0 0 0-1.923 1.424c-.61.61-1.109 1.3-1.492 2.056a.75.75 0 0 0 1.339.677a6.513 6.513 0 0 1 1.214-1.673a6.547 6.547 0 0 1 1.984-1.362l1.93 1.93a5.07 5.07 0 0 0-2.876 1.44a5.11 5.11 0 0 0-1.061 1.57a.75.75 0 0 0 1.374.602c.185-.422.437-.8.747-1.11a3.59 3.59 0 0 1 3.36-.959l7.857 7.858a.75.75 0 0 0 1.061-1.06L3.28 2.22zm8.304 6.182l1.584 1.584a6.533 6.533 0 0 1 3.516 1.825a6.797 6.797 0 0 1 1.217 1.692a.75.75 0 0 0 1.335-.683a8.294 8.294 0 0 0-1.492-2.07a8.042 8.042 0 0 0-6.16-2.348zM8.487 5.305L9.713 6.53a10.44 10.44 0 0 1 10.908 4.297a.75.75 0 0 0 1.235-.852a11.946 11.946 0 0 0-13.369-4.67zm4.573 11.134a1.5 1.5 0 1 1-2.121 2.122a1.5 1.5 0 0 1 2.121-2.122z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WifiOff24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
