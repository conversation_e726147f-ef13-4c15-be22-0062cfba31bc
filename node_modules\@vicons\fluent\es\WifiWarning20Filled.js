import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M16.73 7.563c.403.404.783.864 1.115 1.344a.75.75 0 0 1-1.235.852A7.968 7.968 0 0 0 4.4 8.623c-.32.322-.64.712-.932 1.133a.75.75 0 1 1-1.233-.855c.34-.49.716-.95 1.105-1.338a9.468 9.468 0 0 1 13.39 0zM5.478 9.451a6.444 6.444 0 0 1 8.652-.417c-.516.095-1 .39-1.314.887a4.944 4.944 0 0 0-7.192 1.851a.75.75 0 0 1-1.338-.677a6.403 6.403 0 0 1 1.192-1.644zm4.303 6.524l1.075-2.153a1.242 1.242 0 1 0-1.075 2.153zm1.702-3.41l.674-1.348a4.132 4.132 0 0 0-5.044.623a4.145 4.145 0 0 0-.86 1.274a.75.75 0 0 0 1.373.6c.136-.31.32-.586.547-.813c.9-.9 2.289-1.011 3.31-.335zm2.119-2.011l-3.496 6.998A1 1 0 0 0 11.002 19h6.996a1 1 0 0 0 .895-1.448l-3.5-6.999a1 1 0 0 0-1.79 0zm1.395 1.941v3.002a.5.5 0 1 1-1 0v-3.002a.5.5 0 1 1 1 0zm-.5 5.504a.5.5 0 1 1 0-1a.5.5 0 0 1 0 1z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WifiWarning20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
