import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4.75 2A2.75 2.75 0 0 0 2 4.75v8.498a2.75 2.75 0 0 0 2.75 2.75h5.885c.034-.08.074-.16.118-.237l.73-1.262H4.75c-.69 0-1.25-.56-1.25-1.25V6h10.997v1.15a3.8 3.8 0 0 1 1.5-.437V4.75A2.75 2.75 0 0 0 13.247 2H4.75zm9.272 10.1a2.78 2.78 0 0 1 2.769-4.352l-1.311 2.27a.927.927 0 0 0 1.605.927l1.31-2.27a2.78 2.78 0 0 1-2.363 4.575l-2.407 4.17a1.158 1.158 0 0 1-2.006-1.159l2.403-4.161zm-1.46-.955a3.763 3.763 0 0 1 .214-2.076l-1.922-1.923a.5.5 0 0 0-.708.708L12.293 10l-2.147 2.146a.5.5 0 0 0 .708.708l1.708-1.709zM8.354 7.146a.5.5 0 0 1 0 .708L6.207 10l2.147 2.146a.5.5 0 0 1-.708.708l-2.5-2.5a.5.5 0 0 1 0-.708l2.5-2.5a.5.5 0 0 1 .708 0z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowDevTools20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
