"use strict";Object.defineProperty(exports, "__esModule", {value: true});




var _chunk3JXNMKDIjs = require('./chunk-3JXNMKDI.js');



var _chunkZBO2PJECjs = require('./chunk-ZBO2PJEC.js');

// src/index.ts
var logger = _chunkZBO2PJECjs.createLogger.call(void 0, );
function printLog(type, ...args) {
  if (typeof logger[type] === "function")
    logger[type].apply(logger, [_chunkZBO2PJECjs.TAG, ...args]);
}







exports.build = _chunk3JXNMKDIjs.build; exports.defineConfig = _chunk3JXNMKDIjs.defineConfig; exports.dev = _chunk3JXNMKDIjs.dev; exports.logger = logger; exports.printLog = printLog; exports.resolveConfig = _chunk3JXNMKDIjs.resolveConfig;
