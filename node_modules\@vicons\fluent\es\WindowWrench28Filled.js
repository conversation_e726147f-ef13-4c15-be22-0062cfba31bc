import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6.75 3h14.5C23.304 3 25 4.749 25 6.75v7.75h-.054c-.207-.654-.961-1.271-1.4-1.384a1.336 1.336 0 0 0-.046-.015V9.5h-19v11.75a2.25 2.25 0 0 0 2.25 2.25h6.65c-.267.463-.4.981-.4 1.5H6.75A3.75 3.75 0 0 1 3 21.25V6.75C3 4.712 4.72 3 6.75 3zM14 25a2 2 0 0 0 3.414 1.413l3.649-3.647a4.5 4.5 0 0 0 5.89-4.92c-.054-.367-.495-.476-.758-.213l-1.78 1.781a2 2 0 1 1-2.83-2.828l1.782-1.781c.262-.263.154-.704-.213-.758a4.5 4.5 0 0 0-4.92 5.89l-3.648 3.648c-.391.39-.586.903-.586 1.415z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WindowWrench28Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
