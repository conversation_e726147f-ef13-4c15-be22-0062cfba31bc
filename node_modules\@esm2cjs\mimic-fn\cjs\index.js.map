{"version": 3, "sources": ["../esm/index.js"], "sourcesContent": ["const copyProperty = (to, from, property, ignoreNonConfigurable) => {\n\t// `Function#length` should reflect the parameters of `to` not `from` since we keep its body.\n\t// `Function#prototype` is non-writable and non-configurable so can never be modified.\n\tif (property === 'length' || property === 'prototype') {\n\t\treturn;\n\t}\n\n\t// `Function#arguments` and `Function#caller` should not be copied. They were reported to be present in `Reflect.ownKeys` for some devices in React Native (#41), so we explicitly ignore them here.\n\tif (property === 'arguments' || property === 'caller') {\n\t\treturn;\n\t}\n\n\tconst toDescriptor = Object.getOwnPropertyDescriptor(to, property);\n\tconst fromDescriptor = Object.getOwnPropertyDescriptor(from, property);\n\n\tif (!canCopyProperty(toDescriptor, fromDescriptor) && ignoreNonConfigurable) {\n\t\treturn;\n\t}\n\n\tObject.defineProperty(to, property, fromDescriptor);\n};\n\n// `Object.defineProperty()` throws if the property exists, is not configurable and either:\n// - one its descriptors is changed\n// - it is non-writable and its value is changed\nconst canCopyProperty = function (toDescriptor, fromDescriptor) {\n\treturn toDescriptor === undefined || toDescriptor.configurable || (\n\t\ttoDescriptor.writable === fromDescriptor.writable &&\n\t\ttoDescriptor.enumerable === fromDescriptor.enumerable &&\n\t\ttoDescriptor.configurable === fromDescriptor.configurable &&\n\t\t(toDescriptor.writable || toDescriptor.value === fromDescriptor.value)\n\t);\n};\n\nconst changePrototype = (to, from) => {\n\tconst fromPrototype = Object.getPrototypeOf(from);\n\tif (fromPrototype === Object.getPrototypeOf(to)) {\n\t\treturn;\n\t}\n\n\tObject.setPrototypeOf(to, fromPrototype);\n};\n\nconst wrappedToString = (withName, fromBody) => `/* Wrapped ${withName}*/\\n${fromBody}`;\n\nconst toStringDescriptor = Object.getOwnPropertyDescriptor(Function.prototype, 'toString');\nconst toStringName = Object.getOwnPropertyDescriptor(Function.prototype.toString, 'name');\n\n// We call `from.toString()` early (not lazily) to ensure `from` can be garbage collected.\n// We use `bind()` instead of a closure for the same reason.\n// Calling `from.toString()` early also allows caching it in case `to.toString()` is called several times.\nconst changeToString = (to, from, name) => {\n\tconst withName = name === '' ? '' : `with ${name.trim()}() `;\n\tconst newToString = wrappedToString.bind(null, withName, from.toString());\n\t// Ensure `to.toString.toString` is non-enumerable and has the same `same`\n\tObject.defineProperty(newToString, 'name', toStringName);\n\tObject.defineProperty(to, 'toString', {...toStringDescriptor, value: newToString});\n};\n\nexport default function mimicFunction(to, from, {ignoreNonConfigurable = false} = {}) {\n\tconst {name} = to;\n\n\tfor (const property of Reflect.ownKeys(from)) {\n\t\tcopyProperty(to, from, property, ignoreNonConfigurable);\n\t}\n\n\tchangePrototype(to, from);\n\tchangeToString(to, from, name);\n\n\treturn to;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAAM,eAAe,CAAC,IAAI,MAAM,UAAU,0BAA0B;AAGnE,MAAI,aAAa,YAAY,aAAa,aAAa;AACtD;AAAA,EACD;AAGA,MAAI,aAAa,eAAe,aAAa,UAAU;AACtD;AAAA,EACD;AAEA,QAAM,eAAe,OAAO,yBAAyB,IAAI,QAAQ;AACjE,QAAM,iBAAiB,OAAO,yBAAyB,MAAM,QAAQ;AAErE,MAAI,CAAC,gBAAgB,cAAc,cAAc,KAAK,uBAAuB;AAC5E;AAAA,EACD;AAEA,SAAO,eAAe,IAAI,UAAU,cAAc;AACnD;AAKA,MAAM,kBAAkB,SAAU,cAAc,gBAAgB;AAC/D,SAAO,iBAAiB,UAAa,aAAa,gBACjD,aAAa,aAAa,eAAe,YACzC,aAAa,eAAe,eAAe,cAC3C,aAAa,iBAAiB,eAAe,iBAC5C,aAAa,YAAY,aAAa,UAAU,eAAe;AAElE;AAEA,MAAM,kBAAkB,CAAC,IAAI,SAAS;AACrC,QAAM,gBAAgB,OAAO,eAAe,IAAI;AAChD,MAAI,kBAAkB,OAAO,eAAe,EAAE,GAAG;AAChD;AAAA,EACD;AAEA,SAAO,eAAe,IAAI,aAAa;AACxC;AAEA,MAAM,kBAAkB,CAAC,UAAU,aAAa,cAAc;AAAA,EAAe;AAE7E,MAAM,qBAAqB,OAAO,yBAAyB,SAAS,WAAW,UAAU;AACzF,MAAM,eAAe,OAAO,yBAAyB,SAAS,UAAU,UAAU,MAAM;AAKxF,MAAM,iBAAiB,CAAC,IAAI,MAAM,SAAS;AAC1C,QAAM,WAAW,SAAS,KAAK,KAAK,QAAQ,KAAK,KAAK;AACtD,QAAM,cAAc,gBAAgB,KAAK,MAAM,UAAU,KAAK,SAAS,CAAC;AAExE,SAAO,eAAe,aAAa,QAAQ,YAAY;AACvD,SAAO,eAAe,IAAI,YAAY,EAAC,GAAG,oBAAoB,OAAO,YAAW,CAAC;AAClF;AAEe,SAAR,cAA+B,IAAI,MAAM,EAAC,wBAAwB,MAAK,IAAI,CAAC,GAAG;AACrF,QAAM,EAAC,KAAI,IAAI;AAEf,aAAW,YAAY,QAAQ,QAAQ,IAAI,GAAG;AAC7C,iBAAa,IAAI,MAAM,UAAU,qBAAqB;AAAA,EACvD;AAEA,kBAAgB,IAAI,IAAI;AACxB,iBAAe,IAAI,MAAM,IAAI;AAE7B,SAAO;AACR;", "names": []}